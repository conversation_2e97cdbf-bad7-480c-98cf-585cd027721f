# ProductVideo Backend

A comprehensive backend platform for Shopify merchants to bulk-generate AI product videos, preview/regenerate variants, and push to Shopify with analytics and billing.

## 🚀 Features

- **Shopify Integration**: OAuth flow, webhooks, product sync, media push
- **AI Video Generation**: Multiple providers (Synthesia, Revid, mock)
- **Video Processing**: MP4/HLS transcoding, thumbnail generation, subtitles
- **Analytics**: Event tracking, conversion funnels, A/B testing
- **Billing**: Stripe integration with metered usage
- **Multi-tenant**: Secure tenant isolation
- **Admin Tools**: Webhook replay, job reprocessing, manual operations
- **Monitoring**: Prometheus metrics, health checks

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   Worker Pool   │
│   (React)       │◄──►│   (FastAPI)     │◄──►│   (Python)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │     Redis       │    │   S3 Storage    │
│   (Database)    │    │   (Queue)       │    │   (Videos)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🛠️ Tech Stack

- **Backend**: Python 3.11, FastAPI, SQLAlchemy, Alembic
- **Database**: PostgreSQL 15
- **Queue**: Redis with async workers
- **Storage**: S3-compatible (MinIO/AWS S3) + CloudFront CDN
- **Video Processing**: FFmpeg for transcoding
- **Monitoring**: Prometheus + Grafana
- **Deployment**: Docker, Docker Compose, Terraform

## 📋 Prerequisites

- Python 3.11+
- Docker & Docker Compose
- PostgreSQL 15
- Redis 7
- FFmpeg (for video processing)

## 🚀 Quick Start

### 1. Clone and Setup

```bash
git clone <repository-url>
cd backend
```

### 2. Environment Configuration

Create `.env` file:

```bash
cp .env.example .env
```

Configure required variables:

```env
# Database
DATABASE_URL=postgresql://app_user:dev_password@localhost:5432/ecommerce_db

# Redis
REDIS_URL=redis://localhost:6379/0

# Shopify
SHOPIFY_API_KEY=your_shopify_api_key
SHOPIFY_API_SECRET=your_shopify_api_secret

# Stripe
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Storage
S3_BUCKET_NAME=your-bucket-name
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key

# Video Generation
VIDEO_PROVIDER=mock  # or revid_ai, synthesia
```

### 3. Docker Development Setup

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f backend

# Run migrations
docker-compose exec backend python -m alembic upgrade head

# Create admin user
docker-compose exec backend python scripts/create_admin.py
```

### 4. Local Development Setup

```bash
# Install dependencies
pip install -e .

# Start PostgreSQL and Redis
docker-compose up -d postgres redis minio

# Run migrations
alembic upgrade head

# Start API server
uvicorn servers.api.main:app --reload --port 8000

# Start worker (in another terminal)
python -m servers.worker.main
```

## 📚 API Documentation

Once running, visit:
- **API Docs**: http://localhost:8000/docs
- **Admin Panel**: http://localhost:8000/admin
- **Health Check**: http://localhost:8000/health

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DATABASE_URL` | PostgreSQL connection string | Required |
| `REDIS_URL` | Redis connection string | Required |
| `SHOPIFY_API_KEY` | Shopify app API key | Required |
| `SHOPIFY_API_SECRET` | Shopify app secret | Required |
| `STRIPE_SECRET_KEY` | Stripe secret key | Required |
| `VIDEO_PROVIDER` | AI video provider | `mock` |
| `STORAGE_PROVIDER` | Storage provider | `s3` |
| `ENVIRONMENT` | Environment name | `development` |

### Video Providers

Configure video generation providers:

```env
# Mock (for development)
VIDEO_PROVIDER=mock

# Revid AI
VIDEO_PROVIDER=revid_ai
REVID_AI_API_KEY=your_api_key

# Synthesia
VIDEO_PROVIDER=synthesia
SYNTHESIA_API_KEY=your_api_key
```

## 🧪 Testing

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=src --cov-report=html

# Run specific test file
pytest tests/test_video_generation.py

# Run integration tests
pytest tests/integration/
```

## 📊 Monitoring

### Health Checks

- **API Health**: `GET /health`
- **Database**: `GET /health/db`
- **Redis**: `GET /health/redis`
- **Storage**: `GET /health/storage`

### Metrics

Prometheus metrics available at `/metrics`:

- Request counts and latency
- Queue depth and processing time
- Database connection pool
- Video generation success/failure rates

### Logs

Structured JSON logging with correlation IDs:

```bash
# View logs
docker-compose logs -f backend

# Filter by level
docker-compose logs backend | grep ERROR
```

## 🔐 Security

### Authentication

- JWT tokens for API access
- Shopify OAuth for merchant authentication
- Admin endpoints protected with basic auth

### Data Protection

- Encrypted database connections
- S3 bucket encryption at rest
- Webhook signature verification
- Rate limiting on all endpoints

## 🚀 Deployment

### Production with Terraform

```bash
cd terraform

# Initialize
terraform init

# Plan deployment
terraform plan -var-file="production.tfvars"

# Deploy
terraform apply -var-file="production.tfvars"
```

### Environment-specific configs

- `terraform/dev.tfvars` - Development
- `terraform/staging.tfvars` - Staging  
- `terraform/production.tfvars` - Production

## 📈 Scaling

### Horizontal Scaling

- Multiple API server instances behind load balancer
- Worker pool scaling based on queue depth
- Database read replicas for analytics queries

### Performance Optimization

- Redis caching for frequently accessed data
- CDN for video delivery
- Database query optimization with indexes
- Async processing for heavy operations

## 🛠️ Development

### Database Migrations

```bash
# Create migration
alembic revision --autogenerate -m "Add new table"

# Apply migrations
alembic upgrade head

# Rollback
alembic downgrade -1
```

### Adding New Modules

1. Create module directory in `src/modules/`
2. Add models, schemas, services, and router
3. Register router in `servers/api/main.py`
4. Add models import in `core/db/models.py`

### Code Quality

```bash
# Format code
black src/
isort src/

# Lint
flake8 src/
mypy src/

# Security scan
bandit -r src/
```

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   ```bash
   # Check PostgreSQL is running
   docker-compose ps postgres
   
   # Check connection
   docker-compose exec postgres psql -U app_user -d ecommerce_db
   ```

2. **Redis Connection Failed**
   ```bash
   # Check Redis is running
   docker-compose ps redis
   
   # Test connection
   docker-compose exec redis redis-cli ping
   ```

3. **Video Processing Fails**
   ```bash
   # Check FFmpeg installation
   ffmpeg -version
   
   # Check worker logs
   docker-compose logs worker
   ```

4. **Shopify Webhooks Not Working**
   - Verify webhook URL is publicly accessible
   - Check HMAC signature verification
   - Ensure webhook topics are subscribed

### Debug Mode

```bash
# Enable debug logging
export LOG_LEVEL=DEBUG

# Run with debugger
python -m debugpy --listen 5678 --wait-for-client -m uvicorn servers.api.main:app
```

## 📞 Support

- **Documentation**: [Link to docs]
- **Issues**: [GitHub Issues]
- **Discord**: [Community Discord]
- **Email**: <EMAIL>

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.
