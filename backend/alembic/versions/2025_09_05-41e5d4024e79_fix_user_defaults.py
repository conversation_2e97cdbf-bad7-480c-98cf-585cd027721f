"""fix_user_defaults

Revision ID: 41e5d4024e79
Revises: add_billing_fields_to_tenants
Create Date: 2025-09-05 11:01:37.600198

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '41e5d4024e79'
down_revision: Union[str, Sequence[str], None] = 'add_billing_fields_to_tenants'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Update existing users with NULL values to have proper defaults
    op.execute("UPDATE users SET is_verified = false WHERE is_verified IS NULL")
    op.execute("UPDATE users SET role = 'user' WHERE role IS NULL")
    op.execute("UPDATE users SET is_active = true WHERE is_active IS NULL")


def downgrade() -> None:
    """Downgrade schema."""
    pass
