"""add_comprehensive_shopify_product_fields

Revision ID: 54140672eb97
Revises: d53972404733
Create Date: 2025-09-05 19:13:35.007555

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '54140672eb97'
down_revision: Union[str, Sequence[str], None] = 'd53972404733'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('product_variants', sa.Column('inventory_policy', sa.String(), nullable=True))
    op.add_column('product_variants', sa.Column('fulfillment_service', sa.Text(), nullable=True))
    op.add_column('product_variants', sa.Column('inventory_item', sa.Text(), nullable=True))
    op.add_column('products', sa.Column('published_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('products', sa.Column('online_store_url', sa.String(), nullable=True))
    op.add_column('products', sa.Column('online_store_preview_url', sa.String(), nullable=True))
    op.add_column('products', sa.Column('has_only_default_variant', sa.Boolean(), nullable=True))
    op.add_column('products', sa.Column('has_out_of_stock_variants', sa.Boolean(), nullable=True))
    op.add_column('products', sa.Column('is_gift_card', sa.Boolean(), nullable=True))
    op.add_column('products', sa.Column('requires_selling_plan', sa.Boolean(), nullable=True))
    op.add_column('products', sa.Column('total_inventory', sa.Integer(), nullable=True))
    op.add_column('products', sa.Column('total_variants', sa.Integer(), nullable=True))
    op.add_column('products', sa.Column('tracks_inventory', sa.Boolean(), nullable=True))
    op.add_column('products', sa.Column('template_suffix', sa.String(), nullable=True))
    op.add_column('products', sa.Column('gift_card_template_suffix', sa.String(), nullable=True))
    op.add_column('products', sa.Column('legacy_resource_id', sa.String(), nullable=True))
    op.add_column('products', sa.Column('featured_image', sa.Text(), nullable=True))
    op.add_column('products', sa.Column('options', sa.Text(), nullable=True))
    op.add_column('products', sa.Column('product_category', sa.Text(), nullable=True))
    op.add_column('products', sa.Column('seo', sa.Text(), nullable=True))
    op.add_column('products', sa.Column('metafields', sa.Text(), nullable=True))
    op.add_column('products', sa.Column('collections', sa.Text(), nullable=True))
    op.add_column('products', sa.Column('compare_at_price_range', sa.Text(), nullable=True))
    op.add_column('products', sa.Column('price_range', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('products', 'price_range')
    op.drop_column('products', 'compare_at_price_range')
    op.drop_column('products', 'collections')
    op.drop_column('products', 'metafields')
    op.drop_column('products', 'seo')
    op.drop_column('products', 'product_category')
    op.drop_column('products', 'options')
    op.drop_column('products', 'featured_image')
    op.drop_column('products', 'legacy_resource_id')
    op.drop_column('products', 'gift_card_template_suffix')
    op.drop_column('products', 'template_suffix')
    op.drop_column('products', 'tracks_inventory')
    op.drop_column('products', 'total_variants')
    op.drop_column('products', 'total_inventory')
    op.drop_column('products', 'requires_selling_plan')
    op.drop_column('products', 'is_gift_card')
    op.drop_column('products', 'has_out_of_stock_variants')
    op.drop_column('products', 'has_only_default_variant')
    op.drop_column('products', 'online_store_preview_url')
    op.drop_column('products', 'online_store_url')
    op.drop_column('products', 'published_at')
    op.drop_column('product_variants', 'inventory_item')
    op.drop_column('product_variants', 'fulfillment_service')
    op.drop_column('product_variants', 'inventory_policy')
    # ### end Alembic commands ###
