"""add_sync_settings_to_stores

Revision ID: 547f78bbb290
Revises: 84bd78f85ee2
Create Date: 2025-09-05 12:36:21.653666

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '547f78bbb290'
down_revision: Union[str, Sequence[str], None] = '84bd78f85ee2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Add sync settings columns to stores table
    op.add_column('stores', sa.Column('product_sync_interval', sa.String(), nullable=True, default='Every 30 minutes'))
    op.add_column('stores', sa.Column('inventory_sync_interval', sa.String(), nullable=True, default='Every 5 minutes'))
    op.add_column('stores', sa.Column('customer_sync_interval', sa.String(), nullable=True, default='Every hour'))


def downgrade() -> None:
    """Downgrade schema."""
    # Remove sync settings columns from stores table
    op.drop_column('stores', 'customer_sync_interval')
    op.drop_column('stores', 'inventory_sync_interval')
    op.drop_column('stores', 'product_sync_interval')
