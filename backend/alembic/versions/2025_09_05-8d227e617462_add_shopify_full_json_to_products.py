"""add_shopify_full_json_to_products

Revision ID: 8d227e617462
Revises: a0e7b635cccf
Create Date: 2025-09-05 17:04:00.739486

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8d227e617462'
down_revision: Union[str, Sequence[str], None] = 'a0e7b635cccf'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('products', sa.Column('shopify_full_json', sa.Text(), nullable=True))
    op.alter_column('stores', 'product_sync_interval',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('stores', 'inventory_sync_interval',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('stores', 'customer_sync_interval',
               existing_type=sa.VARCHAR(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('stores', 'customer_sync_interval',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('stores', 'inventory_sync_interval',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('stores', 'product_sync_interval',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.drop_column('products', 'shopify_full_json')
    # ### end Alembic commands ###
