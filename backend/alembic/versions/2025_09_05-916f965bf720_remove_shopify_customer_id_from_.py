"""remove_shopify_customer_id_from_customers

Revision ID: 916f965bf720
Revises: 9728198615bf
Create Date: 2025-09-05 18:17:14.760592

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '916f965bf720'
down_revision: Union[str, Sequence[str], None] = '9728198615bf'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Drop the redundant shopify_customer_id column
    op.drop_column('customers', 'shopify_customer_id')


def downgrade() -> None:
    """Downgrade schema."""
    # Add back the shopify_customer_id column
    op.add_column('customers', sa.Column('shopify_customer_id', sa.String(), nullable=True))
