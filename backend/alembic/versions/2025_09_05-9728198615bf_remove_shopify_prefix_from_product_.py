"""remove_shopify_prefix_from_product_columns

Revision ID: 9728198615bf
Revises: 8d227e617462
Create Date: 2025-09-05 18:14:46.757583

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9728198615bf'
down_revision: Union[str, Sequence[str], None] = '8d227e617462'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Rename columns to remove shopify_ prefix
    op.alter_column('products', 'shopify_handle', new_column_name='handle')
    op.alter_column('products', 'shopify_images', new_column_name='images')
    op.alter_column('products', 'shopify_variants', new_column_name='variants')
    op.alter_column('products', 'shopify_full_json', new_column_name='full_json')
    # Drop the redundant shopify_product_id column
    op.drop_column('products', 'shopify_product_id')


def downgrade() -> None:
    """Downgrade schema."""
    # Add back the shopify_product_id column
    op.add_column('products', sa.Column('shopify_product_id', sa.String(), nullable=True))
    # Rename columns back to add shopify_ prefix
    op.alter_column('products', 'handle', new_column_name='shopify_handle')
    op.alter_column('products', 'images', new_column_name='shopify_images')
    op.alter_column('products', 'variants', new_column_name='shopify_variants')
    op.alter_column('products', 'full_json', new_column_name='shopify_full_json')
