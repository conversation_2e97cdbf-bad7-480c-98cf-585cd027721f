"""fix_sync_settings_defaults

Revision ID: a0e7b635cccf
Revises: 547f78bbb290
Create Date: 2025-09-05 13:37:28.774743

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a0e7b635cccf'
down_revision: Union[str, Sequence[str], None] = '547f78bbb290'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Update existing NULL values to defaults
    op.execute("""
        UPDATE stores
        SET product_sync_interval = 'Every 30 minutes'
        WHERE product_sync_interval IS NULL
    """)

    op.execute("""
        UPDATE stores
        SET inventory_sync_interval = 'Every 5 minutes'
        WHERE inventory_sync_interval IS NULL
    """)

    op.execute("""
        UPDATE stores
        SET customer_sync_interval = 'Every hour'
        WHERE customer_sync_interval IS NULL
    """)

    # Make columns non-nullable
    op.alter_column('stores', 'product_sync_interval', nullable=False, existing_type=sa.String())
    op.alter_column('stores', 'inventory_sync_interval', nullable=False, existing_type=sa.String())
    op.alter_column('stores', 'customer_sync_interval', nullable=False, existing_type=sa.String())


def downgrade() -> None:
    """Downgrade schema."""
    # Make columns nullable again
    op.alter_column('stores', 'product_sync_interval', nullable=True, existing_type=sa.String())
    op.alter_column('stores', 'inventory_sync_interval', nullable=True, existing_type=sa.String())
    op.alter_column('stores', 'customer_sync_interval', nullable=True, existing_type=sa.String())
