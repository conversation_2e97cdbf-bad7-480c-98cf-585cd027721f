"""Add billing fields to tenants table

Revision ID: add_billing_fields_to_tenants
Revises: bd2555160b84
Create Date: 2025-09-05 00:02:20.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'add_billing_fields_to_tenants'
down_revision: Union[str, Sequence[str], None] = 'bd2555160b84'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Add billing fields to tenants table
    op.add_column('tenants', sa.Column('stripe_customer_id', sa.String(length=255), nullable=True))
    op.add_column('tenants', sa.Column('trial_ends_at', sa.DateTime(), nullable=True))
    op.add_column('tenants', sa.Column('shopify_shop_domain', sa.String(length=255), nullable=True))
    op.add_column('tenants', sa.Column('shopify_shop_id', sa.String(length=50), nullable=True))
    op.add_column('tenants', sa.Column('billing_email', sa.String(length=255), nullable=True))
    op.add_column('tenants', sa.Column('settings', sa.JSON(), nullable=True, default=dict))


def downgrade() -> None:
    """Downgrade schema."""
    # Remove billing fields from tenants table
    op.drop_column('tenants', 'settings')
    op.drop_column('tenants', 'billing_email')
    op.drop_column('tenants', 'shopify_shop_id')
    op.drop_column('tenants', 'shopify_shop_domain')
    op.drop_column('tenants', 'trial_ends_at')
    op.drop_column('tenants', 'stripe_customer_id')