"""Update Video* to Media* naming convention

Revision ID: bd2555160b84
Revises: 27245db0f0a4
Create Date: 2025-09-05 01:50:19.664810

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'bd2555160b84'
down_revision: Union[str, Sequence[str], None] = '27245db0f0a4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('media_analytics',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('tenant_id', sa.Integer(), nullable=False),
    sa.Column('media_variant_id', sa.Integer(), nullable=False),
    sa.Column('product_id', sa.String(length=255), nullable=False),
    sa.Column('date', sa.DateTime(timezone=True), nullable=False),
    sa.Column('views', sa.Integer(), nullable=False),
    sa.Column('unique_views', sa.Integer(), nullable=False),
    sa.Column('plays', sa.Integer(), nullable=False),
    sa.Column('completions', sa.Integer(), nullable=False),
    sa.Column('total_watch_time', sa.Float(), nullable=False),
    sa.Column('average_watch_time', sa.Float(), nullable=False),
    sa.Column('completion_rate', sa.Float(), nullable=False),
    sa.Column('cta_clicks', sa.Integer(), nullable=False),
    sa.Column('cta_click_rate', sa.Float(), nullable=False),
    sa.Column('add_to_carts', sa.Integer(), nullable=False),
    sa.Column('purchases', sa.Integer(), nullable=False),
    sa.Column('conversion_rate', sa.Float(), nullable=False),
    sa.Column('total_revenue', sa.Float(), nullable=False),
    sa.Column('mobile_views', sa.Integer(), nullable=False),
    sa.Column('desktop_views', sa.Integer(), nullable=False),
    sa.Column('tablet_views', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['media_variant_id'], ['media_variants.id'], ),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_media_analytics_product_date', 'media_analytics', ['product_id', 'date'], unique=False)
    op.create_index('idx_media_analytics_tenant_date', 'media_analytics', ['tenant_id', 'date'], unique=False)
    op.create_index('idx_media_analytics_variant_date', 'media_analytics', ['media_variant_id', 'date'], unique=False)
    op.create_index(op.f('ix_media_analytics_date'), 'media_analytics', ['date'], unique=False)
    op.create_index(op.f('ix_media_analytics_id'), 'media_analytics', ['id'], unique=False)
    op.create_index(op.f('ix_media_analytics_product_id'), 'media_analytics', ['product_id'], unique=False)
    op.drop_index(op.f('idx_video_analytics_product_date'), table_name='video_analytics')
    op.drop_index(op.f('idx_video_analytics_tenant_date'), table_name='video_analytics')
    op.drop_index(op.f('idx_video_analytics_variant_date'), table_name='video_analytics')
    op.drop_index(op.f('ix_video_analytics_date'), table_name='video_analytics')
    op.drop_index(op.f('ix_video_analytics_id'), table_name='video_analytics')
    op.drop_index(op.f('ix_video_analytics_product_id'), table_name='video_analytics')
    op.drop_table('video_analytics')
    op.add_column('analytics_events', sa.Column('media_variant_id', sa.Integer(), nullable=True))
    op.drop_constraint(op.f('analytics_events_video_variant_id_fkey'), 'analytics_events', type_='foreignkey')
    op.create_foreign_key(None, 'analytics_events', 'media_variants', ['media_variant_id'], ['id'])
    op.drop_column('analytics_events', 'video_variant_id')
    op.add_column('conversion_funnels', sa.Column('media_view_event_id', sa.String(length=255), nullable=True))
    op.add_column('conversion_funnels', sa.Column('media_play_event_id', sa.String(length=255), nullable=True))
    op.add_column('conversion_funnels', sa.Column('media_complete_event_id', sa.String(length=255), nullable=True))
    op.add_column('conversion_funnels', sa.Column('first_media_variant_id', sa.Integer(), nullable=True))
    op.add_column('conversion_funnels', sa.Column('converting_media_variant_id', sa.Integer(), nullable=True))
    op.drop_constraint(op.f('conversion_funnels_video_play_event_id_fkey'), 'conversion_funnels', type_='foreignkey')
    op.drop_constraint(op.f('conversion_funnels_first_video_variant_id_fkey'), 'conversion_funnels', type_='foreignkey')
    op.drop_constraint(op.f('conversion_funnels_video_complete_event_id_fkey'), 'conversion_funnels', type_='foreignkey')
    op.drop_constraint(op.f('conversion_funnels_converting_video_variant_id_fkey'), 'conversion_funnels', type_='foreignkey')
    op.drop_constraint(op.f('conversion_funnels_video_view_event_id_fkey'), 'conversion_funnels', type_='foreignkey')
    op.create_foreign_key(None, 'conversion_funnels', 'analytics_events', ['media_complete_event_id'], ['event_id'])
    op.create_foreign_key(None, 'conversion_funnels', 'media_variants', ['converting_media_variant_id'], ['id'])
    op.create_foreign_key(None, 'conversion_funnels', 'analytics_events', ['media_play_event_id'], ['event_id'])
    op.create_foreign_key(None, 'conversion_funnels', 'media_variants', ['first_media_variant_id'], ['id'])
    op.create_foreign_key(None, 'conversion_funnels', 'analytics_events', ['media_view_event_id'], ['event_id'])
    op.drop_column('conversion_funnels', 'first_video_variant_id')
    op.drop_column('conversion_funnels', 'video_complete_event_id')
    op.drop_column('conversion_funnels', 'video_view_event_id')
    op.drop_column('conversion_funnels', 'converting_video_variant_id')
    op.drop_column('conversion_funnels', 'video_play_event_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('conversion_funnels', sa.Column('video_play_event_id', sa.VARCHAR(length=255), autoincrement=False, nullable=True))
    op.add_column('conversion_funnels', sa.Column('converting_video_variant_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('conversion_funnels', sa.Column('video_view_event_id', sa.VARCHAR(length=255), autoincrement=False, nullable=True))
    op.add_column('conversion_funnels', sa.Column('video_complete_event_id', sa.VARCHAR(length=255), autoincrement=False, nullable=True))
    op.add_column('conversion_funnels', sa.Column('first_video_variant_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'conversion_funnels', type_='foreignkey')
    op.drop_constraint(None, 'conversion_funnels', type_='foreignkey')
    op.drop_constraint(None, 'conversion_funnels', type_='foreignkey')
    op.drop_constraint(None, 'conversion_funnels', type_='foreignkey')
    op.drop_constraint(None, 'conversion_funnels', type_='foreignkey')
    op.create_foreign_key(op.f('conversion_funnels_video_view_event_id_fkey'), 'conversion_funnels', 'analytics_events', ['video_view_event_id'], ['event_id'])
    op.create_foreign_key(op.f('conversion_funnels_converting_video_variant_id_fkey'), 'conversion_funnels', 'media_variants', ['converting_video_variant_id'], ['id'])
    op.create_foreign_key(op.f('conversion_funnels_video_complete_event_id_fkey'), 'conversion_funnels', 'analytics_events', ['video_complete_event_id'], ['event_id'])
    op.create_foreign_key(op.f('conversion_funnels_first_video_variant_id_fkey'), 'conversion_funnels', 'media_variants', ['first_video_variant_id'], ['id'])
    op.create_foreign_key(op.f('conversion_funnels_video_play_event_id_fkey'), 'conversion_funnels', 'analytics_events', ['video_play_event_id'], ['event_id'])
    op.drop_column('conversion_funnels', 'converting_media_variant_id')
    op.drop_column('conversion_funnels', 'first_media_variant_id')
    op.drop_column('conversion_funnels', 'media_complete_event_id')
    op.drop_column('conversion_funnels', 'media_play_event_id')
    op.drop_column('conversion_funnels', 'media_view_event_id')
    op.add_column('analytics_events', sa.Column('video_variant_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'analytics_events', type_='foreignkey')
    op.create_foreign_key(op.f('analytics_events_video_variant_id_fkey'), 'analytics_events', 'media_variants', ['video_variant_id'], ['id'])
    op.drop_column('analytics_events', 'media_variant_id')
    op.create_table('video_analytics',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('tenant_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('video_variant_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('product_id', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('date', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('views', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('unique_views', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('plays', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('completions', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('total_watch_time', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=False),
    sa.Column('average_watch_time', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=False),
    sa.Column('completion_rate', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=False),
    sa.Column('cta_clicks', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('cta_click_rate', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=False),
    sa.Column('add_to_carts', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('purchases', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('conversion_rate', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=False),
    sa.Column('total_revenue', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=False),
    sa.Column('mobile_views', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('desktop_views', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('tablet_views', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], name=op.f('video_analytics_tenant_id_fkey')),
    sa.ForeignKeyConstraint(['video_variant_id'], ['media_variants.id'], name=op.f('video_analytics_video_variant_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('video_analytics_pkey'))
    )
    op.create_index(op.f('ix_video_analytics_product_id'), 'video_analytics', ['product_id'], unique=False)
    op.create_index(op.f('ix_video_analytics_id'), 'video_analytics', ['id'], unique=False)
    op.create_index(op.f('ix_video_analytics_date'), 'video_analytics', ['date'], unique=False)
    op.create_index(op.f('idx_video_analytics_variant_date'), 'video_analytics', ['video_variant_id', 'date'], unique=False)
    op.create_index(op.f('idx_video_analytics_tenant_date'), 'video_analytics', ['tenant_id', 'date'], unique=False)
    op.create_index(op.f('idx_video_analytics_product_date'), 'video_analytics', ['product_id', 'date'], unique=False)
    op.drop_index(op.f('ix_media_analytics_product_id'), table_name='media_analytics')
    op.drop_index(op.f('ix_media_analytics_id'), table_name='media_analytics')
    op.drop_index(op.f('ix_media_analytics_date'), table_name='media_analytics')
    op.drop_index('idx_media_analytics_variant_date', table_name='media_analytics')
    op.drop_index('idx_media_analytics_tenant_date', table_name='media_analytics')
    op.drop_index('idx_media_analytics_product_date', table_name='media_analytics')
    op.drop_table('media_analytics')
    # ### end Alembic commands ###
