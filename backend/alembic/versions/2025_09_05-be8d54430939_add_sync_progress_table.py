"""add_sync_progress_table

Revision ID: be8d54430939
Revises: 916f965bf720
Create Date: 2025-09-05 18:26:52.492633

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'be8d54430939'
down_revision: Union[str, Sequence[str], None] = '916f965bf720'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Create sync_progress table
    op.create_table('sync_progress',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('store_id', sa.Integer(), nullable=False),
        sa.Column('sync_type', sa.String(), nullable=False),
        sa.Column('status', sa.String(), nullable=False),
        sa.Column('total_items', sa.Integer(), nullable=True),
        sa.Column('processed_items', sa.Integer(), nullable=True),
        sa.Column('current_batch', sa.Integer(), nullable=True),
        sa.Column('total_batches', sa.Integer(), nullable=True),
        sa.Column('last_update', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    # Create index on store_id and sync_type for faster queries
    op.create_index(op.f('ix_sync_progress_store_id'), 'sync_progress', ['store_id'], unique=False)
    op.create_index(op.f('ix_sync_progress_sync_type'), 'sync_progress', ['sync_type'], unique=False)


def downgrade() -> None:
    """Downgrade schema."""
    # Drop indexes
    op.drop_index(op.f('ix_sync_progress_sync_type'), table_name='sync_progress')
    op.drop_index(op.f('ix_sync_progress_store_id'), table_name='sync_progress')
    # Drop table
    op.drop_table('sync_progress')
