"""Remove variants column from products table to fix naming conflict

Revision ID: d53972404733
Revises: fef86aa1a755
Create Date: 2025-09-05 19:02:36.577497

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd53972404733'
down_revision: Union[str, Sequence[str], None] = 'fef86aa1a755'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('products', 'variants')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('products', sa.Column('variants', sa.TEXT(), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
