"""add_platform_data_to_products

Revision ID: fe79be6472ee
Revises: 54140672eb97
Create Date: 2025-09-05 21:52:55.514773

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'fe79be6472ee'
down_revision: Union[str, Sequence[str], None] = '54140672eb97'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('products', sa.Column('platform_data', sa.Text(), nullable=True))
    op.drop_column('products', 'tracks_inventory')
    op.drop_column('products', 'has_out_of_stock_variants')
    op.drop_column('products', 'online_store_url')
    op.drop_column('products', 'legacy_resource_id')
    op.drop_column('products', 'has_only_default_variant')
    op.drop_column('products', 'total_inventory')
    op.drop_column('products', 'price_range')
    op.drop_column('products', 'online_store_preview_url')
    op.drop_column('products', 'product_category')
    op.drop_column('products', 'compare_at_price_range')
    op.drop_column('products', 'is_gift_card')
    op.drop_column('products', 'requires_selling_plan')
    op.drop_column('products', 'gift_card_template_suffix')
    op.drop_column('products', 'total_variants')
    op.drop_column('products', 'template_suffix')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('products', sa.Column('template_suffix', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('products', sa.Column('total_variants', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('products', sa.Column('gift_card_template_suffix', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('products', sa.Column('requires_selling_plan', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.add_column('products', sa.Column('is_gift_card', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.add_column('products', sa.Column('compare_at_price_range', sa.TEXT(), autoincrement=False, nullable=True))
    op.add_column('products', sa.Column('product_category', sa.TEXT(), autoincrement=False, nullable=True))
    op.add_column('products', sa.Column('online_store_preview_url', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('products', sa.Column('price_range', sa.TEXT(), autoincrement=False, nullable=True))
    op.add_column('products', sa.Column('total_inventory', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('products', sa.Column('has_only_default_variant', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.add_column('products', sa.Column('legacy_resource_id', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('products', sa.Column('online_store_url', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('products', sa.Column('has_out_of_stock_variants', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.add_column('products', sa.Column('tracks_inventory', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.drop_column('products', 'platform_data')
    # ### end Alembic commands ###
