"""refactors shopify

Revision ID: fef86aa1a755
Revises: be8d54430939
Create Date: 2025-09-05 18:58:57.036056

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'fef86aa1a755'
down_revision: Union[str, Sequence[str], None] = 'be8d54430939'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('orders',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('external_id', sa.String(), nullable=False),
    sa.Column('store_id', sa.Integer(), nullable=False),
    sa.Column('customer_id', sa.Integer(), nullable=True),
    sa.Column('order_number', sa.String(), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('email', sa.String(), nullable=True),
    sa.Column('total_price', sa.Float(), nullable=True),
    sa.Column('subtotal_price', sa.Float(), nullable=True),
    sa.Column('total_tax', sa.Float(), nullable=True),
    sa.Column('total_discounts', sa.Float(), nullable=True),
    sa.Column('currency', sa.String(), nullable=True),
    sa.Column('financial_status', sa.String(), nullable=True),
    sa.Column('fulfillment_status', sa.String(), nullable=True),
    sa.Column('full_json', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['customer_id'], ['customers.id'], ),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('external_id')
    )
    op.create_index(op.f('ix_orders_id'), 'orders', ['id'], unique=False)
    op.create_table('order_line_items',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('order_id', sa.Integer(), nullable=False),
    sa.Column('external_id', sa.String(), nullable=True),
    sa.Column('product_id', sa.String(), nullable=True),
    sa.Column('variant_id', sa.String(), nullable=True),
    sa.Column('title', sa.String(), nullable=True),
    sa.Column('sku', sa.String(), nullable=True),
    sa.Column('quantity', sa.Integer(), nullable=True),
    sa.Column('price', sa.Float(), nullable=True),
    sa.Column('total_price', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['order_id'], ['orders.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_order_line_items_id'), 'order_line_items', ['id'], unique=False)
    # Drop dependent tables first (in reverse dependency order)
    op.drop_index(op.f('ix_shopify_order_line_items_external_id'), table_name='shopify_order_line_items')
    op.drop_index(op.f('ix_shopify_order_line_items_id'), table_name='shopify_order_line_items')
    op.drop_table('shopify_order_line_items')
    op.drop_index(op.f('ix_shopify_orders_external_id'), table_name='shopify_orders')
    op.drop_index(op.f('ix_shopify_orders_id'), table_name='shopify_orders')
    op.drop_table('shopify_orders')
    op.drop_index(op.f('ix_shopify_product_variants_external_id'), table_name='shopify_product_variants')
    op.drop_index(op.f('ix_shopify_product_variants_id'), table_name='shopify_product_variants')
    op.drop_table('shopify_product_variants')
    op.drop_index(op.f('ix_shopify_product_media_external_id'), table_name='shopify_product_media')
    op.drop_index(op.f('ix_shopify_product_media_id'), table_name='shopify_product_media')
    op.drop_table('shopify_product_media')
    op.drop_index(op.f('ix_shopify_products_external_id'), table_name='shopify_products')
    op.drop_index(op.f('ix_shopify_products_id'), table_name='shopify_products')
    op.drop_table('shopify_products')
    op.drop_index(op.f('ix_shopify_customers_external_id'), table_name='shopify_customers')
    op.drop_index(op.f('ix_shopify_customers_id'), table_name='shopify_customers')
    op.drop_table('shopify_customers')
    # Finally drop the main table
    op.drop_index(op.f('ix_shopify_integrations_id'), table_name='shopify_integrations')
    op.drop_table('shopify_integrations')
    op.drop_column('media_jobs', 'shopify_product_id')
    op.add_column('media_variants', sa.Column('media_id', sa.String(), nullable=True))
    op.add_column('media_variants', sa.Column('pushed_at', sa.DateTime(timezone=True), nullable=True))
    op.drop_column('media_variants', 'pushed_to_shopify_at')
    op.drop_column('media_variants', 'shopify_media_id')
    op.drop_index(op.f('ix_sync_progress_store_id'), table_name='sync_progress')
    op.drop_index(op.f('ix_sync_progress_sync_type'), table_name='sync_progress')
    op.create_index(op.f('ix_sync_progress_id'), 'sync_progress', ['id'], unique=False)
    op.add_column('tenants', sa.Column('shop_domain', sa.String(length=255), nullable=True))
    op.add_column('tenants', sa.Column('shop_id', sa.String(length=50), nullable=True))
    op.drop_column('tenants', 'shopify_shop_domain')
    op.drop_column('tenants', 'shopify_shop_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('tenants', sa.Column('shopify_shop_id', sa.VARCHAR(length=50), autoincrement=False, nullable=True))
    op.add_column('tenants', sa.Column('shopify_shop_domain', sa.VARCHAR(length=255), autoincrement=False, nullable=True))
    op.drop_column('tenants', 'shop_id')
    op.drop_column('tenants', 'shop_domain')
    op.drop_index(op.f('ix_sync_progress_id'), table_name='sync_progress')
    op.create_index(op.f('ix_sync_progress_sync_type'), 'sync_progress', ['sync_type'], unique=False)
    op.create_index(op.f('ix_sync_progress_store_id'), 'sync_progress', ['store_id'], unique=False)
    op.add_column('media_variants', sa.Column('shopify_media_id', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('media_variants', sa.Column('pushed_to_shopify_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True))
    op.drop_column('media_variants', 'pushed_at')
    op.drop_column('media_variants', 'media_id')
    op.add_column('media_jobs', sa.Column('shopify_product_id', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.create_table('shopify_customers',
    sa.Column('id', sa.INTEGER(), server_default=sa.text("nextval('shopify_customers_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('shopify_integration_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('external_id', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('email', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('first_name', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('last_name', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('phone', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('state', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('accepts_marketing', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('accepts_marketing_updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('orders_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('total_spent', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('shopify_created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('shopify_updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['shopify_integration_id'], ['shopify_integrations.id'], name='shopify_customers_shopify_integration_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='shopify_customers_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_index(op.f('ix_shopify_customers_id'), 'shopify_customers', ['id'], unique=False)
    op.create_index(op.f('ix_shopify_customers_external_id'), 'shopify_customers', ['external_id'], unique=True)
    op.create_table('shopify_order_line_items',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('shopify_integration_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('external_id', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('order_id', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('product_id', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('variant_id', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('title', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('sku', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('quantity', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('price', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('total_discount', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('fulfillment_status', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('fulfillable_quantity', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['order_id'], ['shopify_orders.external_id'], name=op.f('shopify_order_line_items_order_id_fkey')),
    sa.ForeignKeyConstraint(['product_id'], ['shopify_products.external_id'], name=op.f('shopify_order_line_items_product_id_fkey')),
    sa.ForeignKeyConstraint(['shopify_integration_id'], ['shopify_integrations.id'], name=op.f('shopify_order_line_items_shopify_integration_id_fkey')),
    sa.ForeignKeyConstraint(['variant_id'], ['shopify_product_variants.external_id'], name=op.f('shopify_order_line_items_variant_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('shopify_order_line_items_pkey'))
    )
    op.create_index(op.f('ix_shopify_order_line_items_id'), 'shopify_order_line_items', ['id'], unique=False)
    op.create_index(op.f('ix_shopify_order_line_items_external_id'), 'shopify_order_line_items', ['external_id'], unique=True)
    op.create_table('shopify_products',
    sa.Column('id', sa.INTEGER(), server_default=sa.text("nextval('shopify_products_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('shopify_integration_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('external_id', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('title', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('handle', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('product_type', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('vendor', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('status', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('tags', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('featured_image_url', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('images', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('seo_title', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('seo_description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('shopify_created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('shopify_updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('published_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['shopify_integration_id'], ['shopify_integrations.id'], name='shopify_products_shopify_integration_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='shopify_products_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_index(op.f('ix_shopify_products_id'), 'shopify_products', ['id'], unique=False)
    op.create_index(op.f('ix_shopify_products_external_id'), 'shopify_products', ['external_id'], unique=True)
    op.create_table('shopify_product_media',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('shopify_integration_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('external_id', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('product_id', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('media_type', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('alt', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('position', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('url', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('preview_image_url', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('duration', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('status', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('shopify_created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('shopify_updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['product_id'], ['shopify_products.external_id'], name=op.f('shopify_product_media_product_id_fkey')),
    sa.ForeignKeyConstraint(['shopify_integration_id'], ['shopify_integrations.id'], name=op.f('shopify_product_media_shopify_integration_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('shopify_product_media_pkey'))
    )
    op.create_index(op.f('ix_shopify_product_media_id'), 'shopify_product_media', ['id'], unique=False)
    op.create_index(op.f('ix_shopify_product_media_external_id'), 'shopify_product_media', ['external_id'], unique=True)
    op.create_table('shopify_product_variants',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('shopify_integration_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('external_id', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('product_id', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('title', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('sku', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('barcode', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('price', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('compare_at_price', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('inventory_quantity', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('inventory_management', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('inventory_policy', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('weight', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('weight_unit', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('option1', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('option2', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('option3', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('image_url', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('available', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('shopify_created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('shopify_updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['product_id'], ['shopify_products.external_id'], name=op.f('shopify_product_variants_product_id_fkey')),
    sa.ForeignKeyConstraint(['shopify_integration_id'], ['shopify_integrations.id'], name=op.f('shopify_product_variants_shopify_integration_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('shopify_product_variants_pkey'))
    )
    op.create_index(op.f('ix_shopify_product_variants_id'), 'shopify_product_variants', ['id'], unique=False)
    op.create_index(op.f('ix_shopify_product_variants_external_id'), 'shopify_product_variants', ['external_id'], unique=True)
    op.create_table('shopify_orders',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('shopify_integration_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('external_id', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('order_number', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('email', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('customer_id', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('total_price', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('subtotal_price', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('total_tax', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('total_discounts', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('currency', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('financial_status', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('fulfillment_status', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('cancelled_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('cancel_reason', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('source_name', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('referring_site', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('landing_site', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('shopify_created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('shopify_updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('processed_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['customer_id'], ['shopify_customers.external_id'], name=op.f('shopify_orders_customer_id_fkey')),
    sa.ForeignKeyConstraint(['shopify_integration_id'], ['shopify_integrations.id'], name=op.f('shopify_orders_shopify_integration_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('shopify_orders_pkey'))
    )
    op.create_index(op.f('ix_shopify_orders_id'), 'shopify_orders', ['id'], unique=False)
    op.create_index(op.f('ix_shopify_orders_external_id'), 'shopify_orders', ['external_id'], unique=True)
    op.create_table('shopify_integrations',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('store_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], name=op.f('shopify_integrations_store_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('shopify_integrations_pkey'))
    )
    op.create_index(op.f('ix_shopify_integrations_id'), 'shopify_integrations', ['id'], unique=False)
    op.drop_index(op.f('ix_order_line_items_id'), table_name='order_line_items')
    op.drop_table('order_line_items')
    op.drop_index(op.f('ix_orders_id'), table_name='orders')
    op.drop_table('orders')
    # ### end Alembic commands ###
