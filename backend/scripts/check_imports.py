import os
import py_compile
import sys

sys.path.insert(0, 'backend/src')

def check_imports(root_dir):
    errors = []
    for dirpath, dirnames, filenames in os.walk(root_dir):
        for filename in filenames:
            if filename.endswith('.py'):
                filepath = os.path.join(dirpath, filename)
                try:
                    with open(filepath, 'r') as f:
                        code = f.read()
                    compile(code, filepath, 'exec')
                except SyntaxError as e:
                    errors.append(f"Syntax error in {filepath}: {e}")
                except ImportError as e:
                    errors.append(f"Import error in {filepath}: {e}")
                except Exception as e:
                    errors.append(f"Error in {filepath}: {e}")
    return errors

if __name__ == "__main__":
    root_dir = "backend/src"
    errors = check_imports(root_dir)
    if errors:
        print("Import/Syntax errors found:")
        for error in errors:
            print(error)
    else:
        print("All imports are correct. No errors found.")