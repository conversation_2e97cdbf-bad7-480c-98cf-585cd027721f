"""
Email service for sending emails.
"""

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Optional

from core.config import get_settings


class EmailService:
    """Service for sending emails."""

    def __init__(self):
        self.settings = get_settings()

    async def send_email(
        self,
        to_email: str,
        subject: str,
        html_content: str,
        text_content: Optional[str] = None
    ) -> bool:
        """
        Send an email.

        Args:
            to_email: Recipient email address
            subject: Email subject
            html_content: HTML content of the email
            text_content: Plain text content (optional)

        Returns:
            True if email was sent successfully, False otherwise
        """
        if not self.settings.SMTP_USERNAME or not self.settings.SMTP_PASSWORD:
            # For development, just log the email instead of sending
            print(f"EMAIL TO: {to_email}")
            print(f"SUBJECT: {subject}")
            print(f"CONTENT: {html_content}")
            return True

        try:
            # Create message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = f"{self.settings.EMAIL_FROM_NAME} <{self.settings.EMAIL_FROM}>"
            msg['To'] = to_email

            # Add text content
            if text_content:
                msg.attach(MIMEText(text_content, 'plain'))

            # Add HTML content
            msg.attach(MIMEText(html_content, 'html'))

            # Send email
            server = smtplib.SMTP(self.settings.SMTP_SERVER, self.settings.SMTP_PORT)
            server.starttls()
            server.login(self.settings.SMTP_USERNAME, self.settings.SMTP_PASSWORD)
            server.sendmail(self.settings.EMAIL_FROM, to_email, msg.as_string())
            server.quit()

            return True

        except Exception as e:
            print(f"Failed to send email: {e}")
            return False

    async def send_password_reset_email(self, to_email: str, reset_token: str) -> bool:
        """
        Send password reset email.

        Args:
            to_email: User's email address
            reset_token: Password reset token

        Returns:
            True if email was sent successfully
        """
        reset_url = f"{self.settings.FRONTEND_URL}/reset-password?token={reset_token}"

        subject = "Reset Your Password"

        html_content = f"""
        <html>
        <body>
            <h2>Reset Your Password</h2>
            <p>You requested a password reset for your account.</p>
            <p>Click the link below to reset your password:</p>
            <p><a href="{reset_url}">Reset Password</a></p>
            <p>This link will expire in 1 hour.</p>
            <p>If you didn't request this reset, please ignore this email.</p>
            <br>
            <p>Best regards,<br>Your App Team</p>
        </body>
        </html>
        """

        text_content = f"""
        Reset Your Password

        You requested a password reset for your account.

        Click the link below to reset your password:
        {reset_url}

        This link will expire in 1 hour.

        If you didn't request this reset, please ignore this email.

        Best regards,
        Your App Team
        """

        return await self.send_email(to_email, subject, html_content, text_content)


# Create service instance
email_service = EmailService()