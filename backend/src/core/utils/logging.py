import logging
import sys

from pythonjsonlogger import jsonlogger

from core.config import get_settings


def setup_logging():
    """
    Sets up structured JSON logging based on configuration.
    """
    settings = get_settings()
    
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, settings.LOG_LEVEL.upper(), logging.INFO))

    # Clear existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Use a handler that outputs to stdout
    log_handler = logging.StreamHandler(sys.stdout)

    if settings.LOG_FORMAT.lower() == "json":
        # Create a JSON formatter
        formatter = jsonlogger.JsonFormatter(
            "%(asctime)s %(name)s %(levelname)s %(pathname)s %(lineno)d %(message)s"
        )
    else:
        # Use standard text formatter
        formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )

    log_handler.setFormatter(formatter)
    logger.addHandler(log_handler)

    # Set specific loggers to appropriate levels
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
