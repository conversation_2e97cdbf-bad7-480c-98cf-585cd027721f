"""
Dead Letter Queue Service for ProductVideo platform.
Handles failed jobs and messages that need manual intervention.
"""

import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_, or_, func

from core.db.database import Base
from modules.admin.models import DeadLetterQueueEntry

logger = logging.getLogger(__name__)


class DeadLetterQueueService:
    """Service for managing dead letter queue operations."""

    def __init__(self):
        self.max_age_days = 30  # Auto-cleanup after 30 days

    async def add_to_queue(
        self,
        db: AsyncSession,
        job_id: str,
        queue_name: str,
        payload: Dict[str, Any],
        error_message: str,
        priority: str = "normal",
        max_retries: int = 3
    ) -> DeadLetterQueueEntry:
        """
        Add a failed job to the dead letter queue.

        Args:
            db: Database session
            job_id: Original job ID
            queue_name: Queue name where job failed
            payload: Original job payload
            error_message: Error that caused the failure
            priority: Priority level (low, normal, high, critical)
            max_retries: Maximum retry attempts

        Returns:
            Created dead letter queue entry
        """
        try:
            entry = DeadLetterQueueEntry(
                job_id=job_id,
                queue_name=queue_name,
                payload=payload,
                error_message=error_message,
                priority=priority,
                max_retries=max_retries
            )

            db.add(entry)
            await db.commit()
            await db.refresh(entry)

            logger.info(f"Added job {job_id} to dead letter queue")
            return entry

        except Exception as e:
            logger.error(f"Error adding job {job_id} to dead letter queue: {e}")
            raise

    async def get_queue_entries(
        self,
        db: AsyncSession,
        status: Optional[str] = None,
        priority: Optional[str] = None,
        queue_name: Optional[str] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[DeadLetterQueueEntry]:
        """
        Get dead letter queue entries with filtering.

        Args:
            db: Database session
            status: Filter by status
            priority: Filter by priority
            queue_name: Filter by queue name
            limit: Maximum number of entries to return
            offset: Number of entries to skip

        Returns:
            List of dead letter queue entries
        """
        try:
            query = select(DeadLetterQueueEntry)

            if status:
                query = query.where(DeadLetterQueueEntry.status == status)
            if priority:
                query = query.where(DeadLetterQueueEntry.priority == priority)
            if queue_name:
                query = query.where(DeadLetterQueueEntry.queue_name == queue_name)

            query = query.order_by(DeadLetterQueueEntry.created_at.desc())
            query = query.limit(limit).offset(offset)

            result = await db.execute(query)
            return result.scalars().all()

        except Exception as e:
            logger.error(f"Error getting dead letter queue entries: {e}")
            raise

    async def retry_entry(
        self,
        db: AsyncSession,
        entry_id: int,
        user_id: str,
        notes: Optional[str] = None
    ) -> bool:
        """
        Mark an entry for retry.

        Args:
            db: Database session
            entry_id: Dead letter queue entry ID
            user_id: User who initiated the retry
            notes: Optional notes about the retry

        Returns:
            True if successful, False otherwise
        """
        try:
            result = await db.execute(
                select(DeadLetterQueueEntry).where(DeadLetterQueueEntry.id == entry_id)
            )
            entry = result.scalar_one_or_none()

            if not entry:
                logger.error(f"Dead letter queue entry {entry_id} not found")
                return False

            if entry.retry_count >= entry.max_retries:
                logger.error(f"Entry {entry_id} has exceeded max retries")
                return False

            # Update entry status
            entry.status = "retry_scheduled"
            entry.retry_count += 1
            entry.updated_at = datetime.utcnow()
            entry.resolution_notes = notes or f"Retry initiated by user {user_id}"

            await db.commit()

            logger.info(f"Scheduled retry for dead letter queue entry {entry_id}")
            return True

        except Exception as e:
            logger.error(f"Error retrying dead letter queue entry {entry_id}: {e}")
            return False

    async def resolve_entry(
        self,
        db: AsyncSession,
        entry_id: int,
        user_id: str,
        resolution: str,
        notes: Optional[str] = None
    ) -> bool:
        """
        Resolve a dead letter queue entry.

        Args:
            db: Database session
            entry_id: Dead letter queue entry ID
            user_id: User who resolved the entry
            resolution: Resolution type (resolved, discarded)
            notes: Optional resolution notes

        Returns:
            True if successful, False otherwise
        """
        try:
            result = await db.execute(
                select(DeadLetterQueueEntry).where(DeadLetterQueueEntry.id == entry_id)
            )
            entry = result.scalar_one_or_none()

            if not entry:
                logger.error(f"Dead letter queue entry {entry_id} not found")
                return False

            # Update entry
            entry.status = resolution
            entry.resolved_at = datetime.utcnow()
            entry.resolved_by = user_id
            entry.resolution_notes = notes
            entry.updated_at = datetime.utcnow()

            await db.commit()

            logger.info(f"Resolved dead letter queue entry {entry_id} as {resolution}")
            return True

        except Exception as e:
            logger.error(f"Error resolving dead letter queue entry {entry_id}: {e}")
            return False

    async def get_queue_stats(self, db: AsyncSession) -> Dict[str, Any]:
        """
        Get dead letter queue statistics.

        Args:
            db: Database session

        Returns:
            Dictionary with queue statistics
        """
        try:
            # Get counts by status
            status_counts = await db.execute(
                select(
                    DeadLetterQueueEntry.status,
                    func.count(DeadLetterQueueEntry.id)
                ).group_by(DeadLetterQueueEntry.status)
            )

            # Get counts by priority
            priority_counts = await db.execute(
                select(
                    DeadLetterQueueEntry.priority,
                    func.count(DeadLetterQueueEntry.id)
                ).group_by(DeadLetterQueueEntry.priority)
            )

            # Get total count
            total_result = await db.execute(
                select(func.count(DeadLetterQueueEntry.id))
            )
            total_count = total_result.scalar()

            # Get recent entries (last 24 hours)
            yesterday = datetime.utcnow() - timedelta(days=1)
            recent_result = await db.execute(
                select(func.count(DeadLetterQueueEntry.id)).where(
                    DeadLetterQueueEntry.created_at >= yesterday
                )
            )
            recent_count = recent_result.scalar()

            return {
                "total_entries": total_count,
                "recent_entries_24h": recent_count,
                "status_breakdown": dict(status_counts),
                "priority_breakdown": dict(priority_counts)
            }

        except Exception as e:
            logger.error(f"Error getting dead letter queue stats: {e}")
            return {}

    async def cleanup_old_entries(self, db: AsyncSession) -> int:
        """
        Clean up old resolved/discarded entries.

        Args:
            db: Database session

        Returns:
            Number of entries cleaned up
        """
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=self.max_age_days)

            result = await db.execute(
                select(func.count(DeadLetterQueueEntry.id)).where(
                    and_(
                        or_(
                            DeadLetterQueueEntry.status == "resolved",
                            DeadLetterQueueEntry.status == "discarded"
                        ),
                        DeadLetterQueueEntry.created_at < cutoff_date
                    )
                )
            )

            # Note: In a real implementation, you would delete the entries here
            # For now, we'll just count them
            count = result.scalar()

            logger.info(f"Found {count} old entries eligible for cleanup")
            return count

        except Exception as e:
            logger.error(f"Error cleaning up old entries: {e}")
            return 0


# Create service instance
dead_letter_queue_service = DeadLetterQueueService()