"""
Admin models for ProductVideo platform.
Contains models for dead letter queue, monitoring, and system health.
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, Float, JSON
from sqlalchemy.sql import func

from core.db.database import Base


class DeadLetterQueueEntry(Base):
    """Database model for dead letter queue entries."""
    __tablename__ = 'dead_letter_queue'

    id = Column(Integer, primary_key=True, index=True)
    job_id = Column(String(255), nullable=False, index=True)
    queue_name = Column(String(100), nullable=False)
    payload = Column(JSON, nullable=False)
    error_message = Column(Text, nullable=False)
    retry_count = Column(Integer, default=0)
    max_retries = Column(Integer, default=3)
    status = Column(String(20), default="pending")  # pending, processing, resolved, discarded
    priority = Column(String(20), default="normal")  # low, normal, high, critical
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    resolved_at = Column(DateTime(timezone=True), nullable=True)
    resolved_by = Column(String(255), nullable=True)
    resolution_notes = Column(Text, nullable=True)

    def __repr__(self):
        return f"<DeadLetterQueueEntry(id={self.id}, job_id='{self.job_id}', status='{self.status}')>"


class SystemMetric(Base):
    """Database model for system metrics."""
    __tablename__ = 'system_metrics'

    id = Column(Integer, primary_key=True, index=True)
    metric_name = Column(String(100), nullable=False)
    metric_value = Column(Float, nullable=False)
    unit = Column(String(20), nullable=True)
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    metric_metadata = Column(JSON, nullable=True)

    def __repr__(self):
        return f"<SystemMetric(name='{self.metric_name}', value={self.metric_value})>"


class Alert(Base):
    """Database model for system alerts."""
    __tablename__ = 'system_alerts'

    id = Column(Integer, primary_key=True, index=True)
    alert_type = Column(String(50), nullable=False)
    severity = Column(String(20), nullable=False)  # info, warning, error, critical
    title = Column(String(255), nullable=False)
    message = Column(Text, nullable=False)
    status = Column(String(20), default="active")  # active, acknowledged, resolved
    acknowledged_at = Column(DateTime(timezone=True), nullable=True)
    acknowledged_by = Column(String(255), nullable=True)
    resolved_at = Column(DateTime(timezone=True), nullable=True)
    resolved_by = Column(String(255), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    alert_metadata = Column(JSON, nullable=True)

    def __repr__(self):
        return f"<Alert(type='{self.alert_type}', severity='{self.severity}', status='{self.status}')>"


class ServiceHealth(Base):
    """Database model for service health checks."""
    __tablename__ = 'service_health'

    id = Column(Integer, primary_key=True, index=True)
    service_name = Column(String(100), nullable=False)
    status = Column(String(20), nullable=False)  # healthy, unhealthy, unknown
    response_time = Column(Float, nullable=True)  # in milliseconds
    last_check = Column(DateTime(timezone=True), server_default=func.now())
    next_check = Column(DateTime(timezone=True), nullable=True)
    error_message = Column(Text, nullable=True)
    health_metadata = Column(JSON, nullable=True)

    def __repr__(self):
        return f"<ServiceHealth(service='{self.service_name}', status='{self.status}')>"


class WebhookEvent(Base):
    """Database model for tracking webhook events."""
    __tablename__ = 'webhook_events'

    id = Column(Integer, primary_key=True, index=True)
    event_id = Column(String(255), unique=True, index=True, nullable=False)  # Shopify webhook ID
    topic = Column(String(100), nullable=False)
    shop_domain = Column(String(255), nullable=False)
    payload = Column(JSON, nullable=False)
    status = Column(String(20), default="pending")  # pending, processing, completed, failed, retrying
    retry_count = Column(Integer, default=0)
    max_retries = Column(Integer, default=3)
    last_error = Column(Text, nullable=True)
    processing_started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self):
        return f"<WebhookEvent(id={self.id}, topic='{self.topic}', status='{self.status}')>"