"""
Monitoring Service for ProductVideo platform.
Provides system monitoring, health checks, and alerting capabilities.
"""

import logging
import psutil
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_, func

from modules.admin.models import SystemMetric, Alert, ServiceHealth

logger = logging.getLogger(__name__)


class MonitoringService:
    """Service for system monitoring and health checks."""

    def __init__(self):
        self.alert_thresholds = {
            "cpu_usage": {"warning": 70.0, "critical": 90.0},
            "memory_usage": {"warning": 80.0, "critical": 95.0},
            "disk_usage": {"warning": 80.0, "critical": 95.0},
            "response_time": {"warning": 1000.0, "critical": 5000.0}  # milliseconds
        }
        self.health_check_interval = 60  # seconds

    async def collect_system_metrics(self) -> Dict[str, Any]:
        """
        Collect current system metrics.

        Returns:
            Dictionary with system metrics
        """
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            cpu_freq = psutil.cpu_freq()

            # Memory metrics
            memory = psutil.virtual_memory()

            # Disk metrics
            disk = psutil.disk_usage('/')

            # Network metrics
            network = psutil.net_io_counters()

            # Process metrics
            process = psutil.Process()
            process_memory = process.memory_info()
            process_cpu = process.cpu_percent()

            metrics = {
                "cpu": {
                    "usage_percent": cpu_percent,
                    "count": cpu_count,
                    "frequency_mhz": cpu_freq.current if cpu_freq else None
                },
                "memory": {
                    "total_gb": memory.total / (1024**3),
                    "used_gb": memory.used / (1024**3),
                    "available_gb": memory.available / (1024**3),
                    "usage_percent": memory.percent
                },
                "disk": {
                    "total_gb": disk.total / (1024**3),
                    "used_gb": disk.used / (1024**3),
                    "free_gb": disk.free / (1024**3),
                    "usage_percent": (disk.used / disk.total) * 100
                },
                "network": {
                    "bytes_sent_mb": network.bytes_sent / (1024**2),
                    "bytes_recv_mb": network.bytes_recv / (1024**2),
                    "packets_sent": network.packets_sent,
                    "packets_recv": network.packets_recv
                },
                "process": {
                    "memory_mb": process_memory.rss / (1024**2),
                    "cpu_percent": process_cpu
                },
                "timestamp": datetime.utcnow().isoformat()
            }

            return metrics

        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")
            return {}

    async def store_system_metrics(self, db: AsyncSession, metrics: Dict[str, Any]):
        """
        Store system metrics in database.

        Args:
            db: Database session
            metrics: Metrics dictionary
        """
        try:
            # Store CPU metrics
            if "cpu" in metrics:
                cpu_metrics = [
                    SystemMetric(
                        metric_name="cpu_usage_percent",
                        metric_value=metrics["cpu"]["usage_percent"],
                        unit="%",
                        metadata={"cpu_count": metrics["cpu"]["count"]}
                    ),
                    SystemMetric(
                        metric_name="cpu_frequency_mhz",
                        metric_value=metrics["cpu"]["frequency_mhz"] or 0,
                        unit="MHz"
                    )
                ]
                for metric in cpu_metrics:
                    db.add(metric)

            # Store memory metrics
            if "memory" in metrics:
                memory_metrics = [
                    SystemMetric(
                        metric_name="memory_usage_percent",
                        metric_value=metrics["memory"]["usage_percent"],
                        unit="%"
                    ),
                    SystemMetric(
                        metric_name="memory_used_gb",
                        metric_value=metrics["memory"]["used_gb"],
                        unit="GB"
                    )
                ]
                for metric in memory_metrics:
                    db.add(metric)

            # Store disk metrics
            if "disk" in metrics:
                disk_metric = SystemMetric(
                    metric_name="disk_usage_percent",
                    metric_value=metrics["disk"]["usage_percent"],
                    unit="%"
                )
                db.add(disk_metric)

            await db.commit()
            logger.debug("Stored system metrics")

        except Exception as e:
            logger.error(f"Error storing system metrics: {e}")

    async def check_system_health(self) -> Dict[str, Any]:
        """
        Perform system health checks.

        Returns:
            Health check results
        """
        try:
            metrics = await self.collect_system_metrics()

            health_status = {
                "overall_status": "healthy",
                "checks": {},
                "timestamp": datetime.utcnow().isoformat()
            }

            # CPU health check
            if metrics.get("cpu", {}).get("usage_percent", 0) > self.alert_thresholds["cpu_usage"]["critical"]:
                health_status["checks"]["cpu"] = {
                    "status": "critical",
                    "message": f"CPU usage is {metrics['cpu']['usage_percent']:.1f}%"
                }
                health_status["overall_status"] = "unhealthy"
            elif metrics.get("cpu", {}).get("usage_percent", 0) > self.alert_thresholds["cpu_usage"]["warning"]:
                health_status["checks"]["cpu"] = {
                    "status": "warning",
                    "message": f"CPU usage is {metrics['cpu']['usage_percent']:.1f}%"
                }
                if health_status["overall_status"] == "healthy":
                    health_status["overall_status"] = "warning"

            # Memory health check
            if metrics.get("memory", {}).get("usage_percent", 0) > self.alert_thresholds["memory_usage"]["critical"]:
                health_status["checks"]["memory"] = {
                    "status": "critical",
                    "message": f"Memory usage is {metrics['memory']['usage_percent']:.1f}%"
                }
                health_status["overall_status"] = "unhealthy"
            elif metrics.get("memory", {}).get("usage_percent", 0) > self.alert_thresholds["memory_usage"]["warning"]:
                health_status["checks"]["memory"] = {
                    "status": "warning",
                    "message": f"Memory usage is {metrics['memory']['usage_percent']:.1f}%"
                }
                if health_status["overall_status"] == "healthy":
                    health_status["overall_status"] = "warning"

            # Disk health check
            if metrics.get("disk", {}).get("usage_percent", 0) > self.alert_thresholds["disk_usage"]["critical"]:
                health_status["checks"]["disk"] = {
                    "status": "critical",
                    "message": f"Disk usage is {metrics['disk']['usage_percent']:.1f}%"
                }
                health_status["overall_status"] = "unhealthy"
            elif metrics.get("disk", {}).get("usage_percent", 0) > self.alert_thresholds["disk_usage"]["warning"]:
                health_status["checks"]["disk"] = {
                    "status": "warning",
                    "message": f"Disk usage is {metrics['disk']['usage_percent']:.1f}%"
                }
                if health_status["overall_status"] == "healthy":
                    health_status["overall_status"] = "warning"

            return health_status

        except Exception as e:
            logger.error(f"Error performing health checks: {e}")
            return {
                "overall_status": "unknown",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }

    async def create_alert(
        self,
        db: AsyncSession,
        alert_type: str,
        severity: str,
        title: str,
        message: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Alert:
        """
        Create a new alert.

        Args:
            db: Database session
            alert_type: Type of alert
            severity: Alert severity
            title: Alert title
            message: Alert message
            metadata: Additional metadata

        Returns:
            Created alert
        """
        try:
            alert = Alert(
                alert_type=alert_type,
                severity=severity,
                title=title,
                message=message,
                metadata=metadata or {}
            )

            db.add(alert)
            await db.commit()
            await db.refresh(alert)

            logger.info(f"Created alert: {title} ({severity})")
            return alert

        except Exception as e:
            logger.error(f"Error creating alert: {e}")
            raise

    async def get_active_alerts(self, db: AsyncSession) -> List[Alert]:
        """
        Get all active alerts.

        Args:
            db: Database session

        Returns:
            List of active alerts
        """
        try:
            result = await db.execute(
                select(Alert).where(Alert.status == "active").order_by(Alert.created_at.desc())
            )
            return result.scalars().all()

        except Exception as e:
            logger.error(f"Error getting active alerts: {e}")
            return []

    async def acknowledge_alert(
        self,
        db: AsyncSession,
        alert_id: int,
        user_id: str,
        notes: Optional[str] = None
    ) -> bool:
        """
        Acknowledge an alert.

        Args:
            db: Database session
            alert_id: Alert ID
            user_id: User who acknowledged
            notes: Optional notes

        Returns:
            True if successful
        """
        try:
            result = await db.execute(
                select(Alert).where(Alert.id == alert_id)
            )
            alert = result.scalar_one_or_none()

            if not alert:
                return False

            alert.status = "acknowledged"
            alert.acknowledged_at = datetime.utcnow()
            alert.acknowledged_by = user_id
            if notes:
                alert.metadata = alert.metadata or {}
                alert.metadata["acknowledgement_notes"] = notes

            await db.commit()
            logger.info(f"Alert {alert_id} acknowledged by {user_id}")
            return True

        except Exception as e:
            logger.error(f"Error acknowledging alert {alert_id}: {e}")
            return False

    async def resolve_alert(
        self,
        db: AsyncSession,
        alert_id: int,
        user_id: str,
        notes: Optional[str] = None
    ) -> bool:
        """
        Resolve an alert.

        Args:
            db: Database session
            alert_id: Alert ID
            user_id: User who resolved
            notes: Optional notes

        Returns:
            True if successful
        """
        try:
            result = await db.execute(
                select(Alert).where(Alert.id == alert_id)
            )
            alert = result.scalar_one_or_none()

            if not alert:
                return False

            alert.status = "resolved"
            alert.resolved_at = datetime.utcnow()
            alert.resolved_by = user_id
            if notes:
                alert.metadata = alert.metadata or {}
                alert.metadata["resolution_notes"] = notes

            await db.commit()
            logger.info(f"Alert {alert_id} resolved by {user_id}")
            return True

        except Exception as e:
            logger.error(f"Error resolving alert {alert_id}: {e}")
            return False

    async def get_health_status(self) -> Dict[str, Any]:
        """
        Get comprehensive health status.

        Returns:
            Health status dictionary
        """
        try:
            health_check = await self.check_system_health()
            metrics = await self.collect_system_metrics()

            return {
                "status": health_check["overall_status"],
                "timestamp": datetime.utcnow().isoformat(),
                "checks": health_check["checks"],
                "metrics": metrics,
                "services": {
                    "database": "unknown",  # Would need actual DB health check
                    "redis": "unknown",     # Would need actual Redis health check
                    "api": "healthy"        # Assuming API is healthy if this runs
                }
            }

        except Exception as e:
            logger.error(f"Error getting health status: {e}")
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }

    async def _collect_metrics(self) -> Dict[str, Any]:
        """
        Collect current system metrics (alias for collect_system_metrics).

        Returns:
            System metrics
        """
        return await self.collect_system_metrics()

    async def _get_alert(self, alert_id: int) -> Optional[Alert]:
        """
        Get alert by ID (placeholder for future implementation).

        Args:
            alert_id: Alert ID

        Returns:
            Alert if found, None otherwise
        """
        # This would need a database session to be fully implemented
        logger.warning("_get_alert called without database session")
        return None

    async def _store_alert(self, alert: Alert):
        """
        Store alert (placeholder for future implementation).

        Args:
            alert: Alert to store
        """
        # This would need a database session to be fully implemented
        logger.warning("_store_alert called without database session")


# Create service instance
monitoring_service = MonitoringService()