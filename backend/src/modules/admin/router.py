import logging
import psutil
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

from fastapi import APIRouter, Depends, HTTPException, Query, status
from pydantic import BaseModel
from sqlalchemy import func, text
from sqlalchemy.orm import Session

# Adjust imports for the new location
from core.db.database import get_db
from modules.auth.models import User, Tenant
from modules.stores.models import Store # Assuming Store is in modules.stores.models
from modules.media_generation.models import MediaJob, MediaVariant, MediaJobStatus, MediaVariantStatus
from modules.auth.router import get_current_user # Assuming get_current_user is in modules.auth.router

# Assuming these services will be available or need to be migrated/created in backend/src/modules
from core.config import get_settings
from core.services.queue_service import celery_service as queue_service
from modules.admin.webhook_service import admin_webhook_service
from modules.admin.dead_letter_queue_service import dead_letter_queue_service
from modules.admin.monitoring_service import monitoring_service

settings = get_settings()

logger = logging.getLogger(__name__)
router = APIRouter(tags=["admin-dashboard"])

# Services are imported above and ready to use
# dead_letter_queue_service and monitoring_service are available

# Placeholder for require_admin - this dependency needs to be defined or imported
async def require_admin(current_user: User = Depends(get_current_user)):
    # This is a placeholder. Implement actual admin check based on your User model.
    if not current_user.is_admin: # Assuming User model has an 'is_admin' attribute
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Admin privileges required")
    return current_user


class DashboardStats(BaseModel):
    """Dashboard statistics model."""
    users: Dict[str, Any]
    videos: Dict[str, Any]
    jobs: Dict[str, Any]
    storage: Dict[str, Any]
    revenue: Dict[str, Any]
    system: Dict[str, Any]


class SystemMetric(BaseModel):
    """System metric model."""
    name: str
    value: float
    unit: str
    status: str
    threshold_warning: float
    threshold_critical: float
    description: str


class ServiceStatus(BaseModel):
    """Service status model."""
    name: str
    status: str
    uptime: float
    last_check: str
    version: Optional[str] = None
    description: str


@router.get("/dashboard/stats", response_model=DashboardStats)
async def get_dashboard_stats(
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Get comprehensive dashboard statistics.
    
    Returns system-wide metrics for admin monitoring.
    """
    try:
        # Calculate date ranges
        today = datetime.utcnow().date()
        yesterday = today - timedelta(days=1)
        last_month = today - timedelta(days=30)
        
        # User statistics
        total_users = db.query(User).count()
        active_users = db.query(User).filter(User.is_active == True).count()
        new_users_today = db.query(User).filter(
            func.date(User.created_at) == today
        ).count()
        
        # Calculate user growth rate
        users_last_month = db.query(User).filter(
            func.date(User.created_at) <= last_month
        ).count()
        user_growth_rate = ((total_users - users_last_month) / max(users_last_month, 1)) * 100
        
        # Video statistics
        total_videos = db.query(MediaVariant).count()
        videos_today = db.query(MediaVariant).filter(
            func.date(MediaVariant.created_at) == today
        ).count()

        # Success rate calculation
        completed_videos = db.query(MediaVariant).filter(
            MediaVariant.status == MediaVariantStatus.READY
        ).count()
        success_rate = (completed_videos / max(total_videos, 1)) * 100

        # Average generation time
        avg_time_result = db.query(
            func.avg(MediaJob.processing_time_seconds)
        ).filter(
            MediaJob.processing_time_seconds.isnot(None)
        ).scalar()
        avg_generation_time = avg_time_result or 0

        # Job statistics
        pending_jobs = db.query(MediaJob).filter(
            MediaJob.status == MediaJobStatus.PENDING
        ).count()
        processing_jobs = db.query(MediaJob).filter(
            MediaJob.status == MediaJobStatus.PROCESSING
        ).count()
        completed_today = db.query(MediaJob).filter(
            MediaJob.status == MediaJobStatus.COMPLETED,
            func.date(MediaJob.completed_at) == today
        ).count()
        failed_today = db.query(MediaJob).filter(
            MediaJob.status == MediaJobStatus.FAILED,
            func.date(MediaJob.updated_at) == today
        ).count()
        
        # Storage statistics
        storage_stats = db.query(
            func.sum(Tenant.storage_used_gb),
            func.sum(Tenant.storage_limit_gb)
        ).first()
        
        total_used_gb = storage_stats[0] or 0
        total_limit_gb = storage_stats[1] or 0
        usage_percentage = (total_used_gb / max(total_limit_gb, 1)) * 100
        
        # Estimated monthly storage cost (mock calculation)
        monthly_cost = total_used_gb * 0.023  # AWS S3 standard pricing
        
        # Revenue statistics (mock data - would integrate with billing system)
        mrr = 15000.0  # Mock Monthly Recurring Revenue
        total_revenue = 180000.0  # Mock total revenue
        revenue_today = 500.0  # Mock daily revenue
        revenue_growth_rate = 12.5  # Mock growth rate
        
        # System statistics
        cpu_usage = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # Calculate uptime (mock - would use actual system uptime)
        uptime_hours = 72.5
        
        return DashboardStats(
            users={
                "total": total_users,
                "active": active_users,
                "new_today": new_users_today,
                "growth_rate": user_growth_rate
            },
            videos={
                "total_generated": total_videos,
                "generated_today": videos_today,
                "success_rate": success_rate,
                "avg_generation_time": avg_generation_time
            },
            jobs={
                "pending": pending_jobs,
                "processing": processing_jobs,
                "completed_today": completed_today,
                "failed_today": failed_today
            },
            storage={
                "total_used_gb": total_used_gb,
                "total_limit_gb": total_limit_gb,
                "usage_percentage": usage_percentage,
                "monthly_cost": monthly_cost
            },
            revenue={
                "mrr": mrr,
                "total_revenue": total_revenue,
                "revenue_today": revenue_today,
                "growth_rate": revenue_growth_rate
            },
            system={
                "cpu_usage": cpu_usage,
                "memory_usage": memory.percent,
                "disk_usage": (disk.used / disk.total) * 100,
                "uptime_hours": uptime_hours
            }
        )
        
    except Exception as e:
        logger.exception(f"Failed to get dashboard stats: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get dashboard stats: {str(e)}"
        )


@router.get("/system/metrics")
async def get_system_metrics(
    current_user: User = Depends(require_admin)
):
    """
    Get detailed system metrics.
    
    Returns real-time system performance metrics.
    """
    try:
        # Get system metrics
        cpu_usage = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        network = psutil.net_io_counters()
        
        metrics = [
            SystemMetric(
                name="CPU Usage",
                value=cpu_usage,
                unit="%",
                status="healthy" if cpu_usage < 70 else "warning" if cpu_usage < 90 else "critical",
                threshold_warning=70.0,
                threshold_critical=90.0,
                description="Current CPU utilization across all cores"
            ),
            SystemMetric(
                name="Memory Usage",
                value=memory.percent,
                unit="%",
                status="healthy" if memory.percent < 80 else "warning" if memory.percent < 95 else "critical",
                threshold_warning=80.0,
                threshold_critical=95.0,
                description="Current RAM utilization"
            ),
            SystemMetric(
                name="Disk Usage",
                value=(disk.used / disk.total) * 100,
                unit="%",
                status="healthy" if (disk.used / disk.total) < 0.8 else "warning" if (disk.used / disk.total) < 0.95 else "critical",
                threshold_warning=80.0,
                threshold_critical=95.0,
                description="Current disk space utilization"
            ),
            SystemMetric(
                name="Network I/O",
                value=(network.bytes_sent + network.bytes_recv) / (1024 * 1024),  # MB
                unit="MB",
                status="healthy",
                threshold_warning=1000.0,
                threshold_critical=5000.0,
                description="Total network bytes transferred"
            )
        ]
        
        # Mock service statuses
        services = [
            ServiceStatus(
                name="FastAPI Application",
                status="running",
                uptime=72.5,
                last_check=datetime.utcnow().isoformat(),
                version="1.0.0",
                description="Main application server"
            ),
            ServiceStatus(
                name="PostgreSQL Database",
                status="running",
                uptime=168.2,
                last_check=datetime.utcnow().isoformat(),
                version="14.9",
                description="Primary database server"
            ),
            ServiceStatus(
                name="Redis Cache",
                status="running",
                uptime=120.1,
                last_check=datetime.utcnow().isoformat(),
                version="7.0.12",
                description="Cache and job queue"
            ),
            ServiceStatus(
                name="Video Worker",
                status="running",
                uptime=48.3,
                last_check=datetime.utcnow().isoformat(),
                description="Background video processing"
            )
        ]
        
        # Mock performance metrics
        performance = {
            "requests_per_minute": 150,
            "avg_response_time": 245,
            "error_rate": 0.8,
            "active_connections": 42
        }
        
        # Mock infrastructure status
        infrastructure = {
            "server_count": 3,
            "load_balancer_status": "healthy",
            "database_connections": 15,
            "cache_hit_rate": 94.2
        }
        
        return {
            "metrics": [metric.dict() for metric in metrics],
            "services": [service.dict() for service in services],
            "performance": performance,
            "infrastructure": infrastructure
        }
        
    except Exception as e:
        logger.exception(f"Failed to get system metrics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get system metrics: {str(e)}"
        )


@router.get("/jobs")
async def get_admin_jobs(
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None),
    type: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Get jobs for admin monitoring.
    
    Returns paginated list of jobs with filtering options.
    """
    try:
        # Build query
        query = db.query(MediaJob)

        # Apply filters
        if status and status != 'all':
            query = query.filter(MediaJob.status == status)

        if search:
            query = query.filter(
                MediaJob.id.ilike(f"%{search}%") |
                MediaJob.product_id.ilike(f"%{search}%")
            )

        # Get total count
        total = query.count()

        # Apply pagination
        offset = (page - 1) * per_page
        jobs = query.order_by(MediaJob.created_at.desc()).offset(offset).limit(per_page).all()
        
        # Format jobs for response
        job_data = []
        for job in jobs:
            job_data.append({
                "id": str(job.id),
                "type": "media_generation",
                "status": job.status.value,
                "priority": "normal",  # Mock priority
                "created_at": job.created_at.isoformat(),
                "started_at": job.started_at.isoformat() if job.started_at else None,
                "completed_at": job.completed_at.isoformat() if job.completed_at else None,
                "processing_time_seconds": None,  # Not in MediaJob model
                "attempts": 1,  # Mock attempts
                "max_attempts": 3,  # Mock max attempts
                "error_message": job.error_message,
                "payload": {
                    "product_id": job.product_id,
                    "template_id": job.template_id,
                    "voice_id": job.voice_id
                },
                "metadata": {
                    "tenant_id": job.tenant_id
                },
                "tenant_id": job.tenant_id,
                "worker_id": None  # Mock worker ID
            })
        
        return {
            "jobs": job_data,
            "total": total,
            "page": page,
            "per_page": per_page,
            "total_pages": (total + per_page - 1) // per_page
        }
        
    except Exception as e:
        logger.exception(f"Failed to get admin jobs: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get jobs: {str(e)}"
        )


@router.get("/jobs/stats")
async def get_job_stats(
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Get job queue statistics.
    
    Returns comprehensive job processing metrics.
    """
    try:
        # Get job counts by status
        total_jobs = db.query(MediaJob).count()
        pending_jobs = db.query(MediaJob).filter(MediaJob.status == MediaJobStatus.PENDING).count()
        processing_jobs = db.query(MediaJob).filter(MediaJob.status == MediaJobStatus.PROCESSING).count()
        completed_jobs = db.query(MediaJob).filter(MediaJob.status == MediaJobStatus.COMPLETED).count()
        failed_jobs = db.query(MediaJob).filter(MediaJob.status == MediaJobStatus.FAILED).count()

        # Calculate average processing time
        avg_time_result = db.query(
            func.avg(MediaJob.processing_time_seconds)
        ).filter(
            MediaJob.processing_time_seconds.isnot(None)
        ).scalar()
        avg_processing_time = avg_time_result or 0

        # Calculate throughput (jobs completed in last hour)
        one_hour_ago = datetime.utcnow() - timedelta(hours=1)
        throughput_per_hour = db.query(MediaJob).filter(
            MediaJob.status == MediaJobStatus.COMPLETED,
            MediaJob.completed_at >= one_hour_ago
        ).count()
        
        # Calculate error rate
        error_rate = (failed_jobs / max(total_jobs, 1)) * 100

        # Get queue stats
        queue_stats = queue_service.get_queue_stats()

        return {
            "total_jobs": total_jobs,
            "pending_jobs": pending_jobs,
            "processing_jobs": processing_jobs,
            "completed_jobs": completed_jobs,
            "failed_jobs": failed_jobs,
            "avg_processing_time": avg_processing_time,
            "throughput_per_hour": throughput_per_hour,
            "error_rate": error_rate,
            "queue_stats": queue_stats
        }
        
    except Exception as e:
        logger.exception(f"Failed to get job stats: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get job stats: {str(e)}"
        )


@router.post("/jobs/{job_id}/retry")
async def retry_job(
    job_id: str,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Retry a failed job.
    
    Requeues a failed job for processing.
    """
    try:
        # Find the job
        job = db.query(MediaJob).filter(MediaJob.id == int(job_id)).first()
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Job not found"
            )

        if job.status != MediaJobStatus.FAILED:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Can only retry failed jobs"
            )

        # Reset job status
        job.status = MediaJobStatus.PENDING
        job.error_message = None
        job.started_at = None
        job.completed_at = None
        job.processing_time_seconds = None
        
        db.commit()
        
        # Re-queue the job in the job queue system
        # Assuming the original job type was 'video_generation' and payload was {'video_job_id': job.id}
        await job_queue.enqueue_job(
            job_type="video_generation", # Assuming this is the job type
            payload={"video_job_id": job.id},
            priority=job_queue.JobPriority.NORMAL, # Or determine based on original job's priority
            max_attempts=job.max_attempts, # Use original max attempts
            metadata={"original_external_job_id": job.external_job_id, "retry_attempt": job.attempts + 1}
        )
        
        return {"success": True, "message": "Job queued for retry"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to retry job: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retry job: {str(e)}"
        )


@router.post("/jobs/{job_id}/cancel")
async def cancel_job(
    job_id: str,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Cancel a pending or processing job.
    
    Stops job execution and marks it as cancelled.
    """
    try:
        # Find the job
        job = db.query(MediaJob).filter(MediaJob.id == int(job_id)).first()
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Job not found"
            )

        if job.status not in [MediaJobStatus.PENDING, MediaJobStatus.PROCESSING]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Can only cancel pending or processing jobs"
            )

        # Cancel the job
        job.status = MediaJobStatus.CANCELLED
        job.completed_at = datetime.utcnow()
        
        db.commit()
        
        # Cancel the job in the job queue system
        await job_queue.cancel_job(job.external_job_id) # Assuming cancel_job takes external_job_id
        
        return {"success": True, "message": "Job cancelled"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to cancel job: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to cancel job: {str(e)}"
        )


@router.delete("/jobs/{job_id}")
async def delete_job(
    job_id: str,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Delete a completed, failed, or cancelled job.
    
    Permanently removes job from the system.
    """
    try:
        # Find the job
        job = db.query(MediaJob).filter(MediaJob.id == int(job_id)).first()
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Job not found"
            )

        if job.status in [MediaJobStatus.PENDING, MediaJobStatus.PROCESSING]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete active jobs. Cancel first."
            )

        # Delete associated variants
        db.query(MediaVariant).filter(MediaVariant.job_id == job.id).delete()

        # Delete the job
        db.delete(job)
        db.commit()
        
        return {"success": True, "message": "Job deleted"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to delete job: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete job: {str(e)}"
        )


@router.get("/monitoring/health")
async def get_health_status(
    current_user: User = Depends(require_admin)
):
    """
    Get current system health status.

    Returns health check results for all monitored components.
    """
    try:
        health_status = await monitoring_service.get_health_status()
        return health_status

    except Exception as e:
        logger.exception(f"Failed to get health status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get health status: {str(e)}"
        )


@router.get("/monitoring/alerts")
async def get_active_alerts(
    current_user: User = Depends(require_admin)
):
    """
    Get all active alerts.

    Returns list of unresolved alerts ordered by timestamp.
    """
    try:
        alerts = await monitoring_service.get_active_alerts(db)
        return {
            "alerts": [], # Mock response for now
            "total": 0
        }

    except Exception as e:
        logger.exception(f"Failed to get active alerts: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get active alerts: {str(e)}"
        )


@router.post("/monitoring/alerts/{alert_id}/resolve")
async def resolve_alert(
    alert_id: str,
    current_user: User = Depends(require_admin)
):
    """
    Manually resolve an alert.

    Marks an alert as resolved by an administrator.
    """
    try:
        alert = await monitoring_service._get_alert(alert_id)
        # if not alert:
        #     raise HTTPException(
        #         status_code=status.HTTP_404_NOT_FOUND,
        #         detail="Alert not found"
        #     )

        # if alert.resolved:
        #     raise HTTPException(
        #         status_code=status.HTTP_400_BAD_REQUEST,
        #         detail="Alert is already resolved"
        #     )

        # # Resolve the alert
        # alert.resolved = True
        # alert.resolved_at = datetime.utcnow()
        # await monitoring_service._store_alert(alert)

        return {"success": True, "message": "Alert resolved (mock)"}

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to resolve alert: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to resolve alert: {str(e)}"
        )


@router.get("/monitoring/metrics/current")
async def get_current_metrics(
    current_user: User = Depends(require_admin)
):
    """
    Get current system metrics.

    Returns real-time system performance metrics.
    """
    try:
        metrics = await monitoring_service._collect_metrics()
        return {
            "metrics": {}, # Mock response for now
            "timestamp": datetime.utcnow().isoformat(),
            "thresholds": {}
        }

    except Exception as e:
        logger.exception(f"Failed to get current metrics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get current metrics: {str(e)}"
        )


# Webhook Admin Endpoints
@router.get("/webhooks")
async def get_webhook_events(
    tenant_id: Optional[int] = Query(None),
    status: Optional[str] = Query(None),
    event_type: Optional[str] = Query(None),
    limit: int = Query(100, le=1000),
    offset: int = Query(0, ge=0),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get webhook events with filtering."""
    try:
        from plugins.shopify.webhook_service import WebhookProcessingStatus

        status_enum = None
        if status:
            try:
                status_enum = WebhookProcessingStatus(status.upper())
            except ValueError:
                raise HTTPException(status_code=400, detail=f"Invalid status: {status}")

        events = await admin_webhook_service.get_webhook_events(
            db, tenant_id, status_enum, event_type, limit, offset
        )

        return {
            "events": events,
            "total": len(events),
            "limit": limit,
            "offset": offset
        }

    except Exception as e:
        logger.error(f"Error getting webhook events: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/webhooks/{webhook_id}/replay")
async def replay_webhook(
    webhook_id: int,
    force: bool = Query(False),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Replay a webhook event."""
    try:
        result = await admin_webhook_service.replay_webhook(db, webhook_id, force)
        return result

    except Exception as e:
        logger.error(f"Error replaying webhook {webhook_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/webhooks/replay-batch")
async def replay_webhooks_batch(
    webhook_ids: List[int],
    force: bool = Query(False),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Replay multiple webhook events."""
    try:
        result = await admin_webhook_service.replay_webhooks_batch(db, webhook_ids, force)
        return result

    except Exception as e:
        logger.error(f"Error replaying webhooks batch: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/webhooks/replay-failed")
async def replay_failed_webhooks(
    tenant_id: Optional[int] = Query(None),
    hours_back: int = Query(24, ge=1, le=168),  # Max 1 week
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Replay all failed webhooks from the last N hours."""
    try:
        result = await admin_webhook_service.replay_failed_webhooks(db, tenant_id, hours_back)
        return result

    except Exception as e:
        logger.error(f"Error replaying failed webhooks: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/jobs/{job_id}/reprocess")
async def reprocess_video_job(
    job_id: int,
    force: bool = Query(False),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Reprocess a video job."""
    try:
        result = await admin_webhook_service.reprocess_video_job(db, job_id, force)
        return result

    except Exception as e:
        logger.error(f"Error reprocessing job {job_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/media/push")
async def manual_media_push(
    variant_id: int,
    product_id: str,
    platform: str = Query("shopify"),
    shop_domain: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Manually push a video variant to a platform."""
    try:
        # Import the generic media service
        from plugins import get_media_service
        media_service = get_media_service()

        # If shop_domain is provided, try to determine platform from store
        if shop_domain and not platform:
            store = db.query(Store).filter(Store.shop_domain == shop_domain).first()
            if store:
                # Determine platform from store type (you might need to add a platform field to Store model)
                platform = getattr(store, 'platform', 'shopify')  # Default to shopify for now

        result = await media_service.push_media_to_product(
            store_type=platform,
            shop_domain=shop_domain,
            product_id=product_id,
            media_url="",  # Would need to get from variant
            alt_text=f"Product video - variant {variant_id}"
        )
        return result

    except Exception as e:
        logger.error(f"Error pushing variant {variant_id} to {platform}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/system/stats")
async def get_system_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get system statistics for admin dashboard."""
    try:
        stats = await admin_webhook_service.get_system_stats(db)
        return stats

    except Exception as e:
        logger.error(f"Error getting system stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))
