"""
Admin webhook service for ProductVideo platform.
Handles webhook replay, manual processing, and admin operations.
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, func

from core.services.base_service import BaseService

# Import from admin models
from modules.admin.models import WebhookEvent
from plugins.shopify.webhook_service import WebhookProcessingStatus
from modules.media_generation.models import MediaJob, MediaJobStatus
from core.services.queue_service import celery_service as queue_service

# Import the correct webhook service
from plugins.shopify.webhook_service import ShopifyWebhookService
from modules.stores.models import Store

logger = logging.getLogger(__name__)


class AdminWebhookService(BaseService[WebhookEvent, dict, dict]):
    """Service for admin webhook operations."""

    async def get_webhook_events(
        self,
        db: AsyncSession,
        tenant_id: Optional[int] = None,
        status: Optional[WebhookProcessingStatus] = None,
        event_type: Optional[str] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[WebhookEvent]:
        """
        Get webhook events with filtering.
        
        Args:
            db: Database session
            tenant_id: Filter by tenant
            status: Filter by status
            event_type: Filter by event type
            limit: Maximum number of events
            offset: Offset for pagination
            
        Returns:
            List of webhook events
        """
        query = select(WebhookEvent).order_by(desc(WebhookEvent.created_at))
        
        if tenant_id:
            query = query.where(WebhookEvent.tenant_id == tenant_id)
        
        if status:
            query = query.where(WebhookEvent.status == status.value)
        
        if event_type:
            query = query.where(WebhookEvent.event_type == event_type)
        
        query = query.limit(limit).offset(offset)
        
        result = await db.execute(query)
        return result.scalars().all()

    async def replay_webhook(
        self,
        db: AsyncSession,
        webhook_id: int,
        force: bool = False
    ) -> Dict[str, Any]:
        """
        Replay a webhook event.
        
        Args:
            db: Database session
            webhook_id: Webhook event ID
            force: Force replay even if already processed
            
        Returns:
            Replay result
        """
        # Get webhook event
        result = await db.execute(
            select(WebhookEvent).where(WebhookEvent.id == webhook_id)
        )
        webhook_event = result.scalar_one_or_none()
        
        if not webhook_event:
            raise ValueError(f"Webhook event {webhook_id} not found")
        
        # Check if already processed
        if webhook_event.status == WebhookProcessingStatus.COMPLETED.value and not force:
            return {
                "status": "skipped",
                "message": "Webhook already processed. Use force=true to replay."
            }
        
        try:
            # Reset webhook status
            webhook_event.status = WebhookProcessingStatus.PENDING.value
            webhook_event.processed_at = None
            webhook_event.error_message = None
            await db.commit()
            
            # Get the store for this webhook
            result = await db.execute(
                select(Store).where(Store.shop_domain == webhook_event.shop_domain)
            )
            store = result.scalar_one_or_none()

            if not store:
                raise ValueError(f"Store not found for shop domain: {webhook_event.shop_domain}")

            # Create webhook service instance for this store
            webhook_service = ShopifyWebhookService(store)

            # Process the webhook
            logger.info(f"Processing webhook {webhook_id} for shop {webhook_event.shop_domain}")
            await webhook_service.process_webhook(
                topic=webhook_event.topic,
                payload=webhook_event.payload,
                db=db,
                event_id=webhook_event.event_id
            )
            
            logger.info(f"Successfully replayed webhook {webhook_id}")
            
            return {
                "status": "success",
                "message": f"Webhook {webhook_id} replayed successfully"
            }
            
        except Exception as e:
            logger.error(f"Failed to replay webhook {webhook_id}: {e}")
            return {
                "status": "error",
                "message": str(e)
            }

    async def replay_webhooks_batch(
        self,
        db: AsyncSession,
        webhook_ids: List[int],
        force: bool = False
    ) -> Dict[str, Any]:
        """
        Replay multiple webhook events.
        
        Args:
            db: Database session
            webhook_ids: List of webhook event IDs
            force: Force replay even if already processed
            
        Returns:
            Batch replay result
        """
        results = []
        successful = 0
        failed = 0
        skipped = 0
        
        for webhook_id in webhook_ids:
            try:
                result = await self.replay_webhook(db, webhook_id, force)
                results.append({
                    "webhook_id": webhook_id,
                    **result
                })
                
                if result["status"] == "success":
                    successful += 1
                elif result["status"] == "skipped":
                    skipped += 1
                else:
                    failed += 1
                    
            except Exception as e:
                results.append({
                    "webhook_id": webhook_id,
                    "status": "error",
                    "message": str(e)
                })
                failed += 1
        
        return {
            "total": len(webhook_ids),
            "successful": successful,
            "failed": failed,
            "skipped": skipped,
            "results": results
        }

    async def replay_failed_webhooks(
        self,
        db: AsyncSession,
        tenant_id: Optional[int] = None,
        hours_back: int = 24
    ) -> Dict[str, Any]:
        """
        Replay all failed webhooks from the last N hours.
        
        Args:
            db: Database session
            tenant_id: Filter by tenant
            hours_back: How many hours back to look
            
        Returns:
            Replay result
        """
        cutoff_time = datetime.utcnow() - timedelta(hours=hours_back)
        
        query = select(WebhookEvent).where(
            and_(
                WebhookEvent.status == WebhookProcessingStatus.FAILED.value,
                WebhookEvent.created_at >= cutoff_time
            )
        )
        
        if tenant_id:
            query = query.where(WebhookEvent.tenant_id == tenant_id)
        
        result = await db.execute(query)
        failed_webhooks = result.scalars().all()
        
        webhook_ids = [w.id for w in failed_webhooks]
        
        if not webhook_ids:
            return {
                "status": "success",
                "message": "No failed webhooks found",
                "total": 0
            }
        
        return await self.replay_webhooks_batch(db, webhook_ids, force=True)

    async def get_video_jobs(
        self,
        db: AsyncSession,
        tenant_id: Optional[int] = None,
        status: Optional[MediaJobStatus] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[MediaJob]:
        """
        Get video jobs with filtering.
        
        Args:
            db: Database session
            tenant_id: Filter by tenant
            status: Filter by status
            limit: Maximum number of jobs
            offset: Offset for pagination
            
        Returns:
            List of video jobs
        """
        query = select(MediaJob).order_by(desc(MediaJob.created_at))

        if tenant_id:
            query = query.where(MediaJob.tenant_id == tenant_id)

        if status:
            query = query.where(MediaJob.status == status)

        query = query.limit(limit).offset(offset)

        result = await db.execute(query)
        return result.scalars().all()

    async def reprocess_video_job(
        self,
        db: AsyncSession,
        job_id: int,
        force: bool = False
    ) -> Dict[str, Any]:
        """
        Reprocess a video job.
        
        Args:
            db: Database session
            job_id: Video job ID
            force: Force reprocessing even if completed
            
        Returns:
            Reprocessing result
        """
        # Get video job
        result = await db.execute(
            select(MediaJob).where(MediaJob.id == job_id)
        )
        video_job = result.scalar_one_or_none()

        if not video_job:
            raise ValueError(f"Video job {job_id} not found")

        # Check if already completed
        if video_job.status == MediaJobStatus.COMPLETED and not force:
            return {
                "status": "skipped",
                "message": "Job already completed. Use force=true to reprocess."
            }

        try:
            # Reset job status
            video_job.status = MediaJobStatus.PENDING
            video_job.error_message = None
            video_job.completed_at = None
            await db.commit()

            # Re-queue the job
            queue_service.enqueue_media_generation(
                tenant_id=video_job.tenant_id,
                job_id=video_job.id,
                product_ids=[video_job.product_id] if video_job.product_id else [],
                template_id=video_job.template_id,
                voice_id=video_job.voice_id
            )
            
            logger.info(f"Successfully requeued video job {job_id}")
            
            return {
                "status": "success",
                "message": f"Video job {job_id} requeued for processing"
            }
            
        except Exception as e:
            logger.error(f"Failed to reprocess video job {job_id}: {e}")
            return {
                "status": "error",
                "message": str(e)
            }


    async def get_system_stats(self, db: AsyncSession) -> Dict[str, Any]:
        """
        Get system statistics for admin dashboard.
        
        Args:
            db: Database session
            
        Returns:
            System statistics
        """
        # Get webhook stats
        webhook_stats = await db.execute(
            select(
                WebhookEvent.status,
                func.count(WebhookEvent.id).label('count')
            ).group_by(WebhookEvent.status)
        )
        webhook_counts = {row.status: row.count for row in webhook_stats}
        
        # Get job stats
        job_stats = await db.execute(
            select(
                MediaJob.status,
                func.count(MediaJob.id).label('count')
            ).group_by(MediaJob.status)
        )
        job_counts = {row.status.value: row.count for row in job_stats}

        # Get recent activity
        recent_webhooks = await db.execute(
            select(func.count(WebhookEvent.id)).where(
                WebhookEvent.created_at >= datetime.utcnow() - timedelta(hours=24)
            )
        )

        recent_jobs = await db.execute(
            select(func.count(MediaJob.id)).where(
                MediaJob.created_at >= datetime.utcnow() - timedelta(hours=24)
            )
        )
        
        return {
            "webhook_stats": webhook_counts,
            "job_stats": job_counts,
            "recent_activity": {
                "webhooks_24h": recent_webhooks.scalar(),
                "jobs_24h": recent_jobs.scalar()
            },
            "timestamp": datetime.utcnow().isoformat()
        }


# Create service instance
admin_webhook_service = AdminWebhookService(WebhookEvent)
