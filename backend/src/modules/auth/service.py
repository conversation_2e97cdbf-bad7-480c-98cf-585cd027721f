from datetime import datetime, timed<PERSON>ta
from typing import Optional
import secrets
import string

from jose import JW<PERSON>rror, jwt
from passlib.context import CryptContext
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

from core.config import get_settings
from modules.auth.models import User, OAuthAccount, PasswordReset
from core.services.email_service import email_service


class AuthService:
    """Service for authentication operations."""

    def __init__(self):
        self.settings = get_settings()
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

    def create_access_token(self, data: dict, expires_delta: Optional[timedelta] = None) -> str:
        """Create JWT access token."""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, self.settings.SECRET_KEY, algorithm=self.settings.ALGORITHM)
        return encoded_jwt

    def verify_token(self, token: str) -> Optional[dict]:
        """Verify JWT token and return payload."""
        try:
            payload = jwt.decode(token, self.settings.SECRET_KEY, algorithms=[self.settings.ALGORITHM])
            return payload
        except JWTError:
            return None

    def hash_password(self, password: str) -> str:
        """Hash a password using bcrypt."""
        return self.pwd_context.hash(password)

    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash."""
        return self.pwd_context.verify(plain_password, hashed_password)

    async def get_by_email(self, db: AsyncSession, email: str) -> Optional[User]:
        """Get a user by email address."""
        from sqlalchemy import select
        result = await db.execute(
            select(User).where(User.email == email)
        )
        return result.scalars().first()

    async def get_user_by_id(self, db: AsyncSession, user_id: int) -> Optional[User]:
        """Get a user by ID."""
        from sqlalchemy import select
        result = await db.execute(
            select(User).where(User.id == user_id)
        )
        return result.scalars().first()

    async def create_user(self, db: AsyncSession, user_data) -> User:
        """Create a new user."""
        user = User(
            email=user_data.email,
            password_hash=self.hash_password(user_data.password),
            first_name=user_data.first_name,
            last_name=user_data.last_name,
            username=user_data.username,
            is_active=True,
            is_verified=False
        )
        db.add(user)
        await db.commit()
        await db.refresh(user)
        return user

    def create_refresh_token(self, data: dict) -> str:
        """Create a refresh JWT token."""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(days=self.settings.REFRESH_TOKEN_EXPIRE_DAYS)
        to_encode.update({"exp": expire, "type": "refresh"})
        encoded_jwt = jwt.encode(to_encode, self.settings.SECRET_KEY, algorithm=self.settings.ALGORITHM)
        return encoded_jwt

    def verify_token(self, token: str, token_type: str = "access") -> Optional[dict]:
        """Verify JWT token and return payload."""
        try:
            payload = jwt.decode(token, self.settings.SECRET_KEY, algorithms=[self.settings.ALGORITHM])
            if payload.get("type") == token_type or token_type == "access":
                return payload
            return None
        except JWTError:
            return None

    async def update_last_login(self, db: AsyncSession, user_id: int):
        """Update user's last login timestamp."""
        from sqlalchemy import select, update
        await db.execute(
            update(User)
            .where(User.id == user_id)
            .values(last_login_at=datetime.utcnow())
        )
        await db.commit()

    async def authenticate_user(self, db: AsyncSession, email: str, password: str) -> Optional[User]:
        """Authenticate user with email and password."""
        user = await self.get_by_email(db, email)
        if not user:
            return None
        if not self.verify_password(password, user.password_hash):
            return None
        return user

    async def get_current_user(self, db: AsyncSession, token: str) -> Optional[User]:
        """Get current user from JWT token."""
        payload = self.verify_token(token)
        if payload is None:
            return None

        user_id_str: str = payload.get("sub")
        if user_id_str is None:
            return None

        # Get user by ID since sub contains user ID, not email
        try:
            user_id = int(user_id_str)
            user = await self.get_user_by_id(db, user_id)
            return user
        except ValueError:
            return None

    async def disconnect_oauth_provider(self, db: AsyncSession, user_id: int, provider: str) -> bool:
        """Disconnect OAuth provider from user account."""
        from sqlalchemy import select, delete

        # Check if the OAuth account exists
        stmt = select(OAuthAccount).where(
            OAuthAccount.user_id == user_id,
            OAuthAccount.provider == provider
        )
        result = await db.execute(stmt)
        oauth_account = result.scalar_one_or_none()

        if not oauth_account:
            return False

        # Delete the OAuth account
        stmt = delete(OAuthAccount).where(
            OAuthAccount.user_id == user_id,
            OAuthAccount.provider == provider
        )
        await db.execute(stmt)
        await db.commit()

        return True

    async def get_user_oauth_providers(self, db: AsyncSession, user_id: int):
        """Get list of connected OAuth providers for a user."""
        from sqlalchemy import select

        stmt = select(OAuthAccount).where(OAuthAccount.user_id == user_id)
        result = await db.execute(stmt)
        oauth_accounts = result.scalars().all()

        return oauth_accounts

    async def initiate_password_reset(self, db: AsyncSession, email: str) -> bool:
        """
        Initiate password reset process.

        Args:
            db: Database session
            email: User's email address

        Returns:
            True if reset was initiated successfully
        """
        # Get user by email
        user = await self.get_by_email(db, email)
        if not user:
            # Return True to prevent email enumeration
            return True

        # Generate reset token
        token = self._generate_reset_token()

        # Create password reset record
        reset_record = PasswordReset(
            user_id=user.id,
            token=token,
            expires_at=datetime.utcnow() + timedelta(hours=1)  # 1 hour expiry
        )

        db.add(reset_record)
        await db.commit()
        await db.refresh(reset_record)

        # Send reset email
        success = await email_service.send_password_reset_email(email, token)

        return success

    async def reset_password(self, db: AsyncSession, token: str, new_password: str) -> bool:
        """
        Reset password using reset token.

        Args:
            db: Database session
            token: Reset token
            new_password: New password

        Returns:
            True if password was reset successfully
        """
        # Find valid reset token
        stmt = select(PasswordReset).where(
            PasswordReset.token == token,
            PasswordReset.is_used == False,
            PasswordReset.expires_at > datetime.utcnow()
        )
        result = await db.execute(stmt)
        reset_record = result.scalar_one_or_none()

        if not reset_record:
            return False

        # Get user
        user = await self.get_user_by_id(db, reset_record.user_id)
        if not user:
            return False

        # Update password
        user.password_hash = self.hash_password(new_password)
        reset_record.is_used = True

        await db.commit()

        return True

    def _generate_reset_token(self) -> str:
        """Generate a secure reset token."""
        alphabet = string.ascii_letters + string.digits
        return ''.join(secrets.choice(alphabet) for _ in range(32))


# Create service instance
auth_service = AuthService()
