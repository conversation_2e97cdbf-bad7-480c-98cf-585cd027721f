"""
AI Image Generation Service for ProductVideo platform.
Provides abstraction layer for different AI image generation providers.
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any

import httpx

from core.config import get_settings
from modules.media_generation.models import BaseMediaProvider, ImageProvider
from modules.media_generation.schemas import MediaGenerationRequest, MediaGenerationResult

logger = logging.getLogger(__name__)
settings = get_settings()


class BananaImageProvider(BaseMediaProvider):
    """Banana image generation provider."""
    
    def __init__(self, api_key: str):
        super().__init__(api_key)
        self.base_url = "https://api.banana.dev/v1"
        self.model_key = "flux-1.1-pro"  # Using Flux 1.1 Pro model
    
    async def generate(self, request: MediaGenerationRequest) -> MediaGenerationResult:
        """Generate images using Banana."""
        try:
            # Create optimized prompt for product images
            prompt = self._create_image_prompt(request)
            
            payload = {
                "modelKey": self.model_key,
                "modelInputs": {
                    "prompt": prompt,
                    "num_images": request.num_images,
                    "width": self._get_width_for_aspect(request.aspect_ratio),
                    "height": self._get_height_for_aspect(request.aspect_ratio),
                    "guidance_scale": 7.5,
                    "num_inference_steps": 50,
                    "seed": -1,  # Random seed
                    "safety_check": True
                }
            }
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            response = await self.client.post(
                f"{self.base_url}/start/v4",
                json=payload,
                headers=headers
            )
            response.raise_for_status()
            
            data = response.json()
            
            # Process generated images
            images = []
            if "modelOutputs" in data and "images" in data["modelOutputs"]:
                for i, image_data in enumerate(data["modelOutputs"]["images"]):
                    images.append({
                        "image_url": image_data.get("url"),
                        "thumbnail_url": image_data.get("thumbnail_url"),
                        "width": self._get_width_for_aspect(request.aspect_ratio),
                        "height": self._get_height_for_aspect(request.aspect_ratio),
                        "style": request.style,
                        "variant_name": f"variant_{i+1}"
                    })
            
            return MediaGenerationResult(
                success=True,
                provider_job_id=data.get("id"),
                images=images,
                estimated_completion_time=data.get("estimated_time", 60)
            )
            
        except Exception as e:
            logger.error(f"Banana image generation failed: {e}")
            return MediaGenerationResult(
                success=False,
                error_message=str(e)
            )
    
    def _create_image_prompt(self, request: MediaGenerationRequest) -> str:
        """Create optimized prompt for Banana image generation."""
        if request.custom_prompt:
            return request.custom_prompt
        
        base_prompt = f"Professional product photography of {request.product_title}"
        
        if request.product_description:
            base_prompt += f", {request.product_description[:150]}"
        
        # Add style-specific elements
        style_prompts = {
            "product_photography": "clean white background, studio lighting, high resolution, commercial photography",
            "lifestyle": "lifestyle setting, natural lighting, in-use context, appealing environment",
            "minimalist": "minimal background, clean composition, simple elegant styling",
            "luxury": "premium setting, sophisticated lighting, high-end presentation, luxury aesthetic",
            "social_media": "trendy, eye-catching, social media optimized, vibrant colors"
        }
        
        style_addition = style_prompts.get(request.style, "professional product photography")
        base_prompt += f", {style_addition}"
        
        # Add quality and technical specifications
        base_prompt += ", 8K resolution, sharp focus, professional lighting, high quality"
        
        return base_prompt
    
    def _get_width_for_aspect(self, aspect_ratio: str) -> int:
        """Get width for aspect ratio."""
        dimensions = {
            "1:1": 1024,
            "16:9": 1920,
            "9:16": 1080,
            "4:5": 1080,
            "3:4": 1080
        }
        return dimensions.get(aspect_ratio, 1024)
    
    def _get_height_for_aspect(self, aspect_ratio: str) -> int:
        """Get height for aspect ratio."""
        dimensions = {
            "1:1": 1024,
            "16:9": 1080,
            "9:16": 1920,
            "4:5": 1350,
            "3:4": 1440
        }
        return dimensions.get(aspect_ratio, 1024)
    
    async def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """Get Banana job status."""
        try:
            headers = {"Authorization": f"Bearer {self.api_key}"}
            response = await self.client.get(
                f"{self.base_url}/check/v4/{job_id}",
                headers=headers
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to get Banana job status: {e}")
            return {"status": "error", "error": str(e)}


class MockImageProvider(BaseMediaProvider):
    """Mock provider for testing."""
    
    def __init__(self, api_key: str = "mock"):
        super().__init__(api_key)
    
    async def generate(self, request: MediaGenerationRequest) -> MediaGenerationResult:
        """Mock image generation."""
        await asyncio.sleep(2)  # Simulate processing time
        
        images = []
        for i in range(request.num_images):
            images.append({
                "image_url": f"https://mock-storage.com/images/{request.product_title}_variant_{i+1}.jpg",
                "thumbnail_url": f"https://mock-storage.com/thumbnails/{request.product_title}_variant_{i+1}.jpg",
                "width": self._get_width_for_aspect(request.aspect_ratio),
                "height": self._get_height_for_aspect(request.aspect_ratio),
                "style": request.style,
                "variant_name": f"variant_{i+1}"
            })
        
        return ImageGenerationResult(
            success=True,
            provider_job_id=f"mock_img_job_{datetime.now().timestamp()}",
            images=images,
            estimated_completion_time=30
        )
    
    def _get_width_for_aspect(self, aspect_ratio: str) -> int:
        """Get width for aspect ratio."""
        return 1024
    
    def _get_height_for_aspect(self, aspect_ratio: str) -> int:
        """Get height for aspect ratio."""
        return 1024
    
    async def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """Mock job status."""
        return {
            "status": "completed",
            "progress": 100,
            "images_ready": 4
        }

    async def download_media(self, media_url: str) -> bytes:
        """Mock image download."""
        return b"mock_image_content"


class AIImageService:
    """Main AI image generation service."""
    
    def __init__(self, provider: ImageProvider = ImageProvider.MOCK, api_key: Optional[str] = None):
        self.provider = provider
        
        # Initialize the appropriate provider
        if provider == ImageProvider.MOCK:
            self.client = MockImageProvider(api_key or "mock")
        elif provider == ImageProvider.BANANA:
            self.client = BananaImageProvider(api_key or settings.BANANA_API_KEY)
        elif provider == ImageProvider.DALL_E:
            # TODO: Implement DALL-E provider
            raise NotImplementedError("DALL-E provider not yet implemented")
        elif provider == ImageProvider.MIDJOURNEY:
            # TODO: Implement Midjourney provider
            raise NotImplementedError("Midjourney provider not yet implemented")
        else:
            raise ValueError(f"Unsupported provider: {provider}")
    
    async def generate_images(self, request: MediaGenerationRequest) -> MediaGenerationResult:
        """Generate images using the configured provider."""
        logger.info(f"Generating images with {self.provider} for product: {request.product_title}")
        return await self.client.generate(request)
    
    async def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """Get job status from the provider."""
        return await self.client.get_job_status(job_id)

    async def download_media(self, media_url: str) -> bytes:
        """Download media from the provider."""
        return await self.client.download_media(media_url)


# Create service instance
ai_image_service = AIImageService(
    provider=ImageProvider(settings.IMAGE_PROVIDER),
    api_key=settings.BANANA_API_KEY
)