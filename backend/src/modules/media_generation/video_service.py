"""
AI Video Generation Service for ProductVideo platform.
Provides abstraction layer for different AI video generation providers.
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any

import httpx

from core.config import get_settings
from modules.media_generation.models import (
    BaseMediaProvider,
    VideoProvider
)
from modules.media_generation.schemas import (
    MediaGenerationRequest,
    MediaGenerationResult
)

logger = logging.getLogger(__name__)
settings = get_settings()


class MockVideoProvider(BaseMediaProvider):
    """Mock provider for testing."""
    
    def __init__(self, api_key: str = "mock"):
        super().__init__(api_key)
    
    async def generate(self, request: MediaGenerationRequest) -> MediaGenerationResult:
        """Mock video generation."""
        await asyncio.sleep(2)  # Simulate processing time
        
        videos = []
        for i in range(request.variants_count):
            videos.append({
                "video_url": f"https://mock-storage.com/videos/{request.product_title}_variant_{i+1}.mp4",
                "thumbnail_url": f"https://mock-storage.com/thumbnails/{request.product_title}_variant_{i+1}.jpg",
                "width": self._get_width_for_aspect(request.aspect_ratio),
                "height": self._get_height_for_aspect(request.aspect_ratio),
                "variant_name": f"variant_{i+1}"
            })
        
        return MediaGenerationResult(
            success=True,
            provider_job_id=f"mock_vid_job_{datetime.now().timestamp()}",
            variants=videos,
            estimated_completion_time=30
        )
    
    def _get_width_for_aspect(self, aspect_ratio: str) -> int:
        """Get width for aspect ratio."""
        return 1920
    
    def _get_height_for_aspect(self, aspect_ratio: str) -> int:
        """Get height for aspect ratio."""
        return 1080
    
    async def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """Mock job status."""
        return {
            "status": "completed",
            "progress": 100,
            "videos_ready": 4
        }

    async def download_media(self, media_url: str) -> bytes:
        """Mock video download."""
        return b"mock_video_content"


class Veo3Provider(BaseMediaProvider):
    """Google Veo 3 video generation provider."""

    def __init__(self, api_key: str):
        super().__init__(api_key)
        self.base_url = "https://generativelanguage.googleapis.com/v1beta"
        self.model = "veo-3"

    async def generate(self, request: MediaGenerationRequest) -> MediaGenerationResult:
        """Generate video using Google Veo 3."""
        try:
            # Create prompt for video generation
            prompt = self._create_video_prompt(request)

            payload = {
                "model": self.model,
                "prompt": prompt,
                "duration": 30,  # 30 seconds
                "aspect_ratio": request.aspect_ratio,
                "quality": "high",
                "style": "product_showcase"
            }

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            response = await self.client.post(
                f"{self.base_url}/videos:generate",
                json=payload,
                headers=headers
            )
            response.raise_for_status()

            data = response.json()

            # Generate 4 variants with different styles
            variants = []
            for i, variant_name in enumerate(["square", "vertical", "horizontal", "story"]):
                variant_aspect = self._get_aspect_ratio_for_variant(variant_name)
                variants.append({
                    "variant_name": variant_name,
                    "video_url": f"{data.get('video_url', '')}&variant={i}",
                    "thumbnail_url": f"{data.get('thumbnail_url', '')}&variant={i}",
                    "duration": 30,
                    "resolution": self._get_resolution_for_aspect(variant_aspect),
                    "aspect_ratio": variant_aspect
                })

            return MediaGenerationResult(
                success=True,
                provider_job_id=data.get("job_id"),
                variants=variants,
                estimated_completion_time=data.get("estimated_time", 180)
            )

        except Exception as e:
            logger.error(f"Veo 3 generation failed: {e}")
            return MediaGenerationResult(
                success=False,
                error_message=str(e)
            )

    def _create_video_prompt(self, request: MediaGenerationRequest) -> str:
        """Create optimized prompt for Veo 3."""
        base_prompt = f"Create a professional product video showcasing {request.product_title}."

        if request.product_description:
            base_prompt += f" Product details: {request.product_description[:200]}"

        base_prompt += " The video should be high-quality, well-lit, with smooth camera movements and professional presentation."

        if request.template_id:
            template_styles = {
                "modern_product_showcase": "modern, clean aesthetic with smooth transitions",
                "dynamic_lifestyle": "dynamic, energetic with lifestyle integration",
                "minimalist_clean": "minimalist, elegant with focus on product details",
                "luxury_premium": "luxury, sophisticated with premium feel"
            }
            style = template_styles.get(request.template_id, "professional product showcase")
            base_prompt += f" Style: {style}."

        return base_prompt

    def _get_aspect_ratio_for_variant(self, variant_name: str) -> str:
        """Get aspect ratio for variant."""
        ratios = {
            "square": "1:1",
            "vertical": "9:16",
            "horizontal": "16:9",
            "story": "9:16"
        }
        return ratios.get(variant_name, "16:9")

    def _get_resolution_for_aspect(self, aspect_ratio: str) -> str:
        """Get resolution for aspect ratio."""
        resolutions = {
            "1:1": "1080x1080",
            "9:16": "1080x1920",
            "16:9": "1920x1080",
            "4:5": "1080x1350"
        }
        return resolutions.get(aspect_ratio, "1920x1080")

    async def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """Get Veo 3 job status."""
        try:
            headers = {"Authorization": f"Bearer {self.api_key}"}
            response = await self.client.get(
                f"{self.base_url}/videos/{job_id}",
                headers=headers
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to get Veo 3 job status: {e}")
            return {"status": "error", "error": str(e)}

    async def download_media(self, media_url: str) -> bytes:
        """Download video from Veo 3."""
        response = await self.client.get(media_url)
        response.raise_for_status()
        return response.content


class RevidAIProvider(BaseMediaProvider):
    """Revid AI video generation provider."""

    def __init__(self, api_key: str):
        super().__init__(api_key)
        self.base_url = "https://api.revid.ai/v1"
    
    async def generate(self, request: MediaGenerationRequest) -> MediaGenerationResult:
        """Generate video using Revid AI."""
        try:
            payload = {
                "product": {
                    "title": request.product_title,
                    "description": request.product_description,
                    "images": request.product_images,
                    "price": request.product_price
                },
                "template_id": request.template_id,
                "voice_id": request.voice_id,
                "aspect_ratio": request.aspect_ratio,
                "locale": request.locale,
                "variants_count": request.variants_count
            }
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            response = await self.client.post(
                f"{self.base_url}/generate",
                json=payload,
                headers=headers
            )
            response.raise_for_status()
            
            data = response.json()
            return MediaGenerationResult(
                success=True,
                provider_job_id=data["job_id"],
                estimated_completion_time=data.get("estimated_time", 300)
            )
            
        except Exception as e:
            logger.error(f"Revid AI generation failed: {e}")
            return MediaGenerationResult(
                success=False,
                error_message=str(e)
            )
    
    async def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """Get Revid AI job status."""
        try:
            headers = {"Authorization": f"Bearer {self.api_key}"}
            response = await self.client.get(
                f"{self.base_url}/jobs/{job_id}",
                headers=headers
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to get Revid AI job status: {e}")
            return {"status": "error", "error": str(e)}
    
    async def download_media(self, media_url: str) -> bytes:
        """Download video from Revid AI."""
        response = await self.client.get(media_url)
        response.raise_for_status()
        return response.content


class AIVideoService:
    """Main AI video generation service."""

    def __init__(self, provider: VideoProvider = VideoProvider.MOCK, api_key: Optional[str] = None):
        self.provider = provider
        if provider == VideoProvider.MOCK:
            self.client = MockVideoProvider(api_key or "mock")
        elif provider == VideoProvider.VEO3:
            self.client = Veo3Provider(api_key or settings.VEO3_API_KEY)
        elif provider == VideoProvider.REVID_AI:
            self.client = RevidAIProvider(api_key or settings.REVID_AI_API_KEY)
        elif provider == VideoProvider.VIDIFY:
            raise NotImplementedError("Vidify provider not yet implemented")
        elif provider == VideoProvider.PRODUCT_STUDIO:
            raise NotImplementedError("Product Studio provider not yet implemented")
        else:
            raise ValueError(f"Unsupported video provider: {provider}")

    async def generate(self, request: MediaGenerationRequest) -> MediaGenerationResult:
        logger.info(f"Generating video with {self.provider} for product: {request.product_title}")
        return await self.client.generate(request)

    async def get_job_status(self, job_id: str) -> Dict[str, Any]:
        return await self.client.get_job_status(job_id)

    async def download_media(self, media_url: str) -> bytes:
        return await self.client.download_media(media_url)


# Create service instance
ai_video_service = AIVideoService()
