from sqlalchemy import Column, Integer, String, Float, Text, DateTime, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from core.db.database import Base


class Product(Base):
    """Product model for e-commerce products."""

    __tablename__ = "products"

    id = Column(Integer, primary_key=True, index=True)
    external_id = Column(String, unique=True, nullable=False)  # Shopify product ID
    title = Column(String, nullable=False)
    description = Column(Text)
    price = Column(Float, nullable=False)
    compare_at_price = Column(Float)
    cost = Column(Float)
    sku = Column(String)
    barcode = Column(String)
    quantity = Column(Integer, default=0)
    weight = Column(Float)
    weight_unit = Column(String, default="kg")
    vendor = Column(String)
    product_type = Column(String)
    tags = Column(String)  # JSON string of tags
    status = Column(String, default="active")  # active, draft, archived
    published = Column(Boolean, default=True)

    # Store relationship
    store_id = Column(Integer, ForeignKey("stores.id"))
    store = relationship("modules.stores.models.Store", back_populates="products")

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Shopify specific fields
    handle = Column(String)
    images = Column(Text)  # JSON string of image URLs
    full_json = Column(Text)  # Full product JSON from Shopify API

    # Platform-agnostic fields
    platform_data = Column(Text)  # JSON string for platform-specific data
    published_at = Column(DateTime(timezone=True))
    featured_image = Column(Text)  # JSON string of featured image
    options = Column(Text)  # JSON string of product options
    seo = Column(Text)  # JSON string of SEO fields
    metafields = Column(Text)  # JSON string of metafields
    collections = Column(Text)  # JSON string of collections


class ProductVariant(Base):
    """Product variant model."""

    __tablename__ = "product_variants"

    id = Column(Integer, primary_key=True, index=True)
    external_id = Column(String, unique=True, nullable=False)  # Shopify variant ID
    product_id = Column(Integer, ForeignKey("products.id"))
    title = Column(String)
    price = Column(Float, nullable=False)
    compare_at_price = Column(Float)
    cost = Column(Float)
    sku = Column(String)
    barcode = Column(String)
    quantity = Column(Integer, default=0)
    weight = Column(Float)
    weight_unit = Column(String, default="kg")
    option1 = Column(String)
    option2 = Column(String)
    option3 = Column(String)
    taxable = Column(Boolean, default=True)
    requires_shipping = Column(Boolean, default=True)

    # Additional Shopify variant fields
    inventory_policy = Column(String)
    fulfillment_service = Column(Text)  # JSON string of fulfillment service
    inventory_item = Column(Text)  # JSON string of inventory item

    # Relationships
    product = relationship("Product", back_populates="variants")

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


# Add relationships to Product model
Product.variants = relationship("ProductVariant", back_populates="product")