"""
Products API Router
"""

import logging
from typing import List

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from modules.auth.router import get_current_user
from modules.auth.models import User
from modules.products.models import Product
from modules.products.schemas import (
    ProductCreate,
    ProductListResponse,
    ProductResponse,
    ProductUpdate
)
from modules.products.service import product_service

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/", response_model=List[ProductListResponse])
async def get_products(
    page: int = 1,
    limit: int = 50,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get all products for the current user's stores."""
    try:
        # Get user's stores
        from modules.stores.models import Store
        from sqlalchemy import select

        stores_result = await db.execute(
            select(Store).filter(Store.owner_id == current_user.id)
        )
        user_stores = stores_result.scalars().all()

        if not user_stores:
            return []

        store_ids = [store.id for store in user_stores]

        # Get products for all user's stores with pagination
        all_products = []
        for store_id in store_ids:
            store_products = await product_service.get_by_store_paginated(db, store_id, page, limit)
            all_products.extend(store_products)

        # Apply pagination to the combined results
        start_idx = (page - 1) * limit
        end_idx = start_idx + limit
        paginated_products = all_products[start_idx:end_idx]

        return paginated_products

    except Exception as e:
        logger.error(f"Error getting products: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/", response_model=ProductResponse)
async def create_product(
    product: ProductCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Create a new product."""
    try:
        # Validate store ownership
        from modules.stores.models import Store
        from sqlalchemy import select

        store_result = await db.execute(
            select(Store).filter(
                Store.id == product.store_id,
                Store.owner_id == current_user.id
            )
        )
        store = store_result.scalar_one_or_none()

        if not store:
            raise HTTPException(
                status_code=403,
                detail="Access denied: store does not belong to user"
            )

        db_product = await product_service.create_product_with_variants(db, product)
        return db_product

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating product: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{product_id}", response_model=ProductResponse)
async def get_product(
    product_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get a specific product."""
    try:
        product = await product_service.get_product_with_variants(db, product_id)

        if not product:
            raise HTTPException(status_code=404, detail="Product not found")

        # Check store ownership
        from modules.stores.models import Store
        from sqlalchemy import select

        store_result = await db.execute(
            select(Store).filter(
                Store.id == product.store_id,
                Store.owner_id == current_user.id
            )
        )
        store = store_result.scalar_one_or_none()

        if not store:
            raise HTTPException(
                status_code=403,
                detail="Access denied: product does not belong to user's store"
            )

        return product

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting product: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{product_id}", response_model=ProductResponse)
async def update_product(
    product_id: int,
    product_update: ProductUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Update a product."""
    try:
        # Get existing product
        product = await product_service.get(db, product_id)
        if not product:
            raise HTTPException(status_code=404, detail="Product not found")

        # Check store ownership
        from modules.stores.models import Store
        from sqlalchemy import select

        store_result = await db.execute(
            select(Store).filter(
                Store.id == product.store_id,
                Store.owner_id == current_user.id
            )
        )
        store = store_result.scalar_one_or_none()

        if not store:
            raise HTTPException(
                status_code=403,
                detail="Access denied: product does not belong to user's store"
            )

        updated_product = await product_service.update(db, db_obj=product, obj_in=product_update)
        return updated_product

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating product: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{product_id}")
async def delete_product(
    product_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Delete a product."""
    try:
        # Get existing product
        product = await product_service.get(db, product_id)
        if not product:
            raise HTTPException(status_code=404, detail="Product not found")

        # Check store ownership
        from modules.stores.models import Store
        from sqlalchemy import select

        store_result = await db.execute(
            select(Store).filter(
                Store.id == product.store_id,
                Store.owner_id == current_user.id
            )
        )
        store = store_result.scalar_one_or_none()

        if not store:
            raise HTTPException(
                status_code=403,
                detail="Access denied: product does not belong to user's store"
            )

        await product_service.remove(db, id=product_id)
        return {"message": "Product deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting product: {e}")
        raise HTTPException(status_code=500, detail=str(e))