from datetime import datetime
from typing import Optional, List, Any
from pydantic import BaseModel

from core.schemas.base_schemas import BaseCreateSchema, BaseResponseSchema, BaseUpdateSchema


class ProductVariantBase(BaseModel):
    """Base product variant schema."""

    external_id: str
    title: Optional[str] = None
    price: float
    compare_at_price: Optional[float] = None
    cost: Optional[float] = None
    sku: Optional[str] = None
    barcode: Optional[str] = None
    quantity: int = 0
    weight: Optional[float] = None
    weight_unit: str = "kg"
    option1: Optional[str] = None
    option2: Optional[str] = None
    option3: Optional[str] = None
    taxable: bool = True
    requires_shipping: bool = True


class ProductVariantResponse(BaseResponseSchema, ProductVariantBase):
    """Product variant response schema."""

    product_id: int


class ProductBase(BaseModel):
    """Base product schema."""

    external_id: str
    title: str
    description: Optional[str] = None
    price: float
    compare_at_price: Optional[float] = None
    cost: Optional[float] = None
    sku: Optional[str] = None
    barcode: Optional[str] = None
    quantity: int = 0
    weight: Optional[float] = None
    weight_unit: str = "kg"
    vendor: Optional[str] = None
    product_type: Optional[str] = None
    tags: Optional[str] = None
    status: str = "active"
    published: bool = True
    handle: Optional[str] = None
    images: Optional[str] = None
    full_json: Optional[str] = None

    # Additional Shopify fields
    published_at: Optional[datetime] = None
    online_store_url: Optional[str] = None
    online_store_preview_url: Optional[str] = None
    has_only_default_variant: Optional[bool] = False
    has_out_of_stock_variants: Optional[bool] = False
    is_gift_card: Optional[bool] = False
    requires_selling_plan: Optional[bool] = False
    total_inventory: Optional[int] = 0
    total_variants: Optional[int] = 0
    tracks_inventory: Optional[bool] = True
    template_suffix: Optional[str] = None
    gift_card_template_suffix: Optional[str] = None
    legacy_resource_id: Optional[str] = None
    featured_image: Optional[str] = None
    options: Optional[str] = None
    product_category: Optional[str] = None
    seo: Optional[str] = None
    metafields: Optional[str] = None
    collections: Optional[str] = None
    compare_at_price_range: Optional[str] = None
    price_range: Optional[str] = None


class ProductCreate(BaseCreateSchema, ProductBase):
    """Schema for creating a product."""

    store_id: int
    variants: Optional[List[ProductVariantBase]] = None


class ProductUpdate(BaseUpdateSchema):
    """Schema for updating a product."""

    title: Optional[str] = None
    description: Optional[str] = None
    price: Optional[float] = None
    compare_at_price: Optional[float] = None
    cost: Optional[float] = None
    sku: Optional[str] = None
    barcode: Optional[str] = None
    quantity: Optional[int] = None
    weight: Optional[float] = None
    weight_unit: Optional[str] = None
    vendor: Optional[str] = None
    product_type: Optional[str] = None
    tags: Optional[str] = None
    status: Optional[str] = None
    published: Optional[bool] = None
    handle: Optional[str] = None
    images: Optional[str] = None

    # Additional Shopify fields
    published_at: Optional[datetime] = None
    online_store_url: Optional[str] = None
    online_store_preview_url: Optional[str] = None
    has_only_default_variant: Optional[bool] = None
    has_out_of_stock_variants: Optional[bool] = None
    is_gift_card: Optional[bool] = None
    requires_selling_plan: Optional[bool] = None
    total_inventory: Optional[int] = None
    total_variants: Optional[int] = None
    tracks_inventory: Optional[bool] = None
    template_suffix: Optional[str] = None
    gift_card_template_suffix: Optional[str] = None
    legacy_resource_id: Optional[str] = None
    featured_image: Optional[str] = None
    options: Optional[str] = None
    product_category: Optional[str] = None
    seo: Optional[str] = None
    metafields: Optional[str] = None
    collections: Optional[str] = None
    compare_at_price_range: Optional[str] = None
    price_range: Optional[str] = None


class ProductListResponse(BaseResponseSchema, ProductBase):
    """Product list response schema (without variants for performance)."""

    store_id: int


class ProductResponse(BaseResponseSchema, ProductBase):
    """Product response schema with full details."""

    store_id: int
    variants: Optional[List[ProductVariantResponse]] = None