"""
Sync Progress Service
Manages sync progress tracking and updates
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from sqlalchemy import select, update, desc
from sqlalchemy.ext.asyncio import AsyncSession

from modules.stores.models import SyncProgress

logger = logging.getLogger(__name__)


class ProgressService:
    """Service for managing sync progress tracking."""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def create_sync_progress(
        self,
        store_id: int,
        sync_type: str,
        total_items: int = 0
    ) -> SyncProgress:
        """Create a new sync progress record."""
        # Calculate total batches (50 items per batch)
        total_batches = (total_items + 49) // 50  # Ceiling division

        sync_progress = SyncProgress(
            store_id=store_id,
            sync_type=sync_type,
            status="running",
            total_items=total_items,
            processed_items=0,
            current_batch=0,
            total_batches=total_batches
        )

        self.db.add(sync_progress)
        await self.db.flush()  # Get the ID without committing
        return sync_progress

    async def update_progress(
        self,
        sync_progress_id: int,
        processed_items: int,
        current_batch: Optional[int] = None,
        status: Optional[str] = None
    ) -> None:
        """Update sync progress."""
        update_data = {
            "processed_items": processed_items,
            "last_update": datetime.now()
        }

        if current_batch is not None:
            update_data["current_batch"] = current_batch

        if status is not None:
            update_data["status"] = status
            if status == "completed":
                update_data["completed_at"] = datetime.now()
            elif status == "failed":
                update_data["completed_at"] = datetime.now()

        stmt = (
            update(SyncProgress)
            .where(SyncProgress.id == sync_progress_id)
            .values(**update_data)
        )

        await self.db.execute(stmt)

    async def get_sync_progress(
        self,
        store_id: int,
        sync_type: str
    ) -> Optional[SyncProgress]:
        """Get the latest sync progress for a store and sync type."""
        result = await self.db.execute(
            select(SyncProgress)
            .where(
                SyncProgress.store_id == store_id,
                SyncProgress.sync_type == sync_type
            )
            .order_by(desc(SyncProgress.created_at))
            .limit(1)
        )

        return result.scalar_one_or_none()

    async def get_all_sync_progress(self, store_id: int) -> List[Dict[str, Any]]:
        """Get all sync progress records for a store."""
        result = await self.db.execute(
            select(SyncProgress)
            .where(SyncProgress.store_id == store_id)
            .order_by(desc(SyncProgress.created_at))
        )

        sync_progress_records = result.scalars().all()
        return [record.to_dict() for record in sync_progress_records]

    async def mark_sync_failed(
        self,
        sync_progress_id: int,
        error_message: str
    ) -> None:
        """Mark a sync as failed with an error message."""
        await self.update_progress(
            sync_progress_id=sync_progress_id,
            processed_items=0,  # Reset progress on failure
            status="failed"
        )

        # Update error message
        stmt = (
            update(SyncProgress)
            .where(SyncProgress.id == sync_progress_id)
            .values(error_message=error_message)
        )

        await self.db.execute(stmt)

    async def cleanup_old_progress(self, days: int = 7) -> None:
        """Clean up old sync progress records older than specified days."""
        from sqlalchemy import delete
        from datetime import timedelta

        cutoff_date = datetime.now() - timedelta(days=days)

        stmt = (
            delete(SyncProgress)
            .where(SyncProgress.created_at < cutoff_date)
        )

        result = await self.db.execute(stmt)
        deleted_count = result.rowcount

        if deleted_count > 0:
            logger.info(f"Cleaned up {deleted_count} old sync progress records")

    async def can_start_sync(
        self,
        store_id: int,
        sync_type: str,
        cooldown_hours: int = 1
    ) -> tuple[bool, str]:
        """
        Check if a sync can be started for the given store and type.

        Returns:
            tuple: (can_start, reason)
        """
        from datetime import timedelta

        # Check for running sync
        running_sync = await self.db.execute(
            select(SyncProgress)
            .where(
                SyncProgress.store_id == store_id,
                SyncProgress.sync_type == sync_type,
                SyncProgress.status == "running"
            )
        )

        if running_sync.scalar_one_or_none():
            return False, f"Sync already running for store {store_id}, type {sync_type}"

        # Check cooldown period
        if cooldown_hours > 0:
            cooldown_cutoff = datetime.now() - timedelta(hours=cooldown_hours)

            recent_sync = await self.db.execute(
                select(SyncProgress)
                .where(
                    SyncProgress.store_id == store_id,
                    SyncProgress.sync_type == sync_type,
                    SyncProgress.completed_at >= cooldown_cutoff
                )
                .order_by(desc(SyncProgress.completed_at))
                .limit(1)
            )

            recent_sync_record = recent_sync.scalar_one_or_none()
            if recent_sync_record:
                time_since_completion = datetime.now() - recent_sync_record.completed_at
                remaining_cooldown = timedelta(hours=cooldown_hours) - time_since_completion
                remaining_minutes = int(remaining_cooldown.total_seconds() / 60)

                return False, f"Sync cooldown active. {remaining_minutes} minutes remaining before next sync allowed"

        return True, "Sync can proceed"