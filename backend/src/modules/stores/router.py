"""
API Router for Store Management
"""

import logging
from typing import List

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from modules.auth.router import get_current_user
from modules.stores.models import Store
from modules.stores.schemas import (
    StoreCreate,
    StoreResponse,
    StoreUpdate,
    StoreConnectionTest,
    StoreConnectionTestRequest,
    SyncSettingsResponse,
    SyncSettingsUpdate,
    SyncJobResponse
)
from modules.stores.service import store_service
from modules.auth.models import User

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/", response_model=List[StoreResponse])
async def get_stores(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get all stores for the current user."""
    stores = await store_service.get_by_owner(db, current_user.id)
    return stores


@router.post("/", response_model=StoreResponse)
async def create_store(
    store: StoreCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Create a new store."""
    db_store = await store_service.create_store(db, store, current_user.id)
    return db_store


@router.get("/{store_id}", response_model=StoreResponse)
async def get_store(
    store_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get a specific store."""
    store = await store_service.get(db, store_id)
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")
    
    # Check ownership
    if store.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this store")
    
    return store


@router.put("/{store_id}", response_model=StoreResponse)
async def update_store(
    store_id: int,
    store_update: StoreUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Update a store."""
    store = await store_service.get(db, store_id)
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")
    
    # Check ownership
    if store.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to update this store")
    
    updated_store = await store_service.update(db, db_obj=store, obj_in=store_update)
    return updated_store


@router.delete("/{store_id}")
async def delete_store(
    store_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Delete a store."""
    store = await store_service.get(db, store_id)
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")
    
    # Check ownership
    if store.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to delete this store")
    
    await store_service.remove(db, id=store_id)
    return {"message": "Store deleted successfully"}


@router.post("/{store_id}/test-connection", response_model=StoreConnectionTest)
async def test_store_connection(
    store_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Test connection to an existing store's platform."""
    store = await store_service.get(db, store_id)
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")

    # Check ownership
    if store.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to test this store")

    connection_result = await store_service.test_connection(store)
    return connection_result


@router.post("/test-connection", response_model=StoreConnectionTest)
async def test_connection_by_credentials(
    request: StoreConnectionTestRequest,
    current_user: User = Depends(get_current_user),
):
    """Test connection using store credentials (for adding new stores)."""
    # Create a temporary store object for testing
    temp_store_data = StoreCreate(
        name=request.name,
        platform=request.platform,
        shop_domain=request.shop_domain,
        admin_access_token=request.admin_access_token,
        storefront_access_token=request.storefront_access_token
    )

    # Convert to store model for testing
    temp_store = Store(**temp_store_data.model_dump())
    temp_store.id = 0  # Temporary ID
    temp_store.owner_id = current_user.id

    connection_result = await store_service.test_connection(temp_store)
    return connection_result


@router.get("/platform/{platform}", response_model=List[StoreResponse])
async def get_stores_by_platform(
    platform: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get all stores for a specific platform."""
    stores = await store_service.get_by_platform(db, platform)
    # Filter by ownership
    user_stores = [store for store in stores if store.owner_id == current_user.id]
    return user_stores


@router.get("/{store_id}/sync-settings", response_model=SyncSettingsResponse)
async def get_sync_settings(
    store_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get sync settings for a specific store."""
    store = await store_service.get(db, store_id)
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")

    # Check ownership
    if store.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this store")

    return SyncSettingsResponse(
        store_id=store.id,
        product_sync_interval=store.product_sync_interval,
        inventory_sync_interval=store.inventory_sync_interval,
        customer_sync_interval=store.customer_sync_interval
    )


@router.put("/{store_id}/sync-settings", response_model=SyncSettingsResponse)
async def update_sync_settings(
    store_id: int,
    settings_update: SyncSettingsUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Update sync settings for a specific store."""
    store = await store_service.get(db, store_id)
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")

    # Check ownership
    if store.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to update this store")

    # Update settings
    update_data = settings_update.model_dump(exclude_unset=True)
    updated_store = await store_service.update(db, db_obj=store, obj_in=update_data)

    return SyncSettingsResponse(
        store_id=updated_store.id,
        product_sync_interval=updated_store.product_sync_interval,
        inventory_sync_interval=updated_store.inventory_sync_interval,
        customer_sync_interval=updated_store.customer_sync_interval
    )


@router.post("/{store_id}/sync/products", response_model=SyncJobResponse)
async def sync_products(
    store_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Trigger product sync for a specific store (background job)."""
    store = await store_service.get(db, store_id)
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")

    # Check ownership
    if store.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to sync this store")

    # Check if store has required credentials
    if not store.admin_access_token or not store.shop_domain:
        raise HTTPException(status_code=400, detail="Store is not properly configured for sync")

    # Import here to avoid circular imports
    from core.services.queue_service import celery_service as queue_service

    # Check if sync can be started
    can_enqueue, reason = await queue_service.can_enqueue_sync(
        store_id=store_id,
        sync_type="products",
        cooldown_hours=1
    )

    if not can_enqueue:
        raise HTTPException(
            status_code=429,  # Too Many Requests
            detail=f"Cannot start sync: {reason}"
        )

    # Enqueue sync job
    job_data = {
        "store_id": store_id,
        "shop_domain": store.shop_domain,
        "access_token": store.admin_access_token,
        "sync_type": "products"
    }

    try:
        job = queue_service.enqueue_sync_job(job_data)
        return SyncJobResponse(
            job_id=job,
            status="queued",
            message="Product sync job has been queued"
        )
    except Exception as e:
        logger.error(f"Failed to enqueue sync job: {e}")
        raise HTTPException(status_code=500, detail="Failed to start sync job")


@router.get("/{store_id}/sync-progress", response_model=List[dict])
async def get_sync_progress(
    store_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get sync progress for a specific store."""
    store = await store_service.get(db, store_id)
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")

    # Check ownership
    if store.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this store")

    from modules.stores.progress_service import ProgressService
    progress_service = ProgressService(db)
    progress_records = await progress_service.get_all_sync_progress(store_id)

    return progress_records


@router.get("/{store_id}/sync-progress/{sync_type}", response_model=dict)
async def get_sync_progress_by_type(
    store_id: int,
    sync_type: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get the latest sync progress for a specific sync type."""
    store = await store_service.get(db, store_id)
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")

    # Check ownership
    if store.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this store")

    from modules.stores.progress_service import ProgressService
    progress_service = ProgressService(db)
    progress_record = await progress_service.get_sync_progress(store_id, sync_type)

    if not progress_record:
        # Return a default sync progress object when no record exists
        from datetime import datetime
        return {
            "id": 0,
            "store_id": store_id,
            "sync_type": sync_type,
            "status": "completed",  # Default to completed when no sync is running
            "total_items": 0,
            "processed_items": 0,
            "current_batch": 0,
            "total_batches": 0,
            "progress_percentage": 0,
            "last_update": datetime.now().isoformat(),
            "created_at": datetime.now().isoformat(),
            "completed_at": None,
            "error_message": None
        }

    return progress_record.to_dict()
