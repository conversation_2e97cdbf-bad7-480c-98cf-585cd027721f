import logging
from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from pydantic import BaseModel
from sqlalchemy.orm import Session

from core.db.database import get_db
from modules.auth.models import User, Tenant
from modules.auth.router import get_current_user
from modules.voices.voice_service import VoiceService, VoiceGender, VoiceStyle, VoiceProvider, TTSRequest
from core.config import get_settings

logger = logging.getLogger(__name__)
router = APIRouter(tags=["voices"])

settings = get_settings()

# Initialize voice service
# TODO: Get provider and API key from settings
voice_service = VoiceService(VoiceProvider.MOCK)


class ScriptGenerationRequest(BaseModel):
    """Request model for script generation."""
    product_title: str
    product_description: Optional[str] = None
    key_features: Optional[List[str]] = None
    price: Optional[float] = None
    style: str = "promotional"  # promotional, informational, testimonial


class VoiceTestRequest(BaseModel):
    """Request model for voice testing."""
    voice_id: str
    text: str = "Hello! This is a test of this voice. How does it sound?"
    speed: float = 1.0
    pitch: float = 1.0


@router.get("/list")
async def list_voices(
    language: Optional[str] = Query(None, description="Filter by language"),
    gender: Optional[VoiceGender] = Query(None, description="Filter by gender"),
    style: Optional[VoiceStyle] = Query(None, description="Filter by style"),
    include_premium: bool = Query(True, description="Include premium voices"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    List available TTS voices.
    
    Returns voices filtered by criteria and user's plan tier.
    """
    try:
        # Get user's tenant to check plan tier
        tenant = db.query(Tenant).filter(Tenant.owner_id == current_user.id).first()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found. Please complete onboarding."
            )
        
        # Adjust premium filter based on plan
        if tenant.plan == "free":
            include_premium = False
        
        # Get voices
        voices = await voice_service.get_available_voices(
            language=language,
            gender=gender,
            style=style,
            include_premium=include_premium
        )
        
        return {
            "voices": [voice.dict() for voice in voices],
            "total_voices": len(voices),
            "filters": {
                "language": language,
                "gender": gender.value if gender else None,
                "style": style.value if style else None,
                "include_premium": include_premium
            },
            "user_plan": tenant.plan
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to list voices: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list voices: {str(e)}"
        )


@router.get("/{voice_id}")
async def get_voice(
    voice_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get details for a specific voice.
    
    Returns voice information if user has access.
    """
    try:
        # Get user's tenant
        tenant = db.query(Tenant).filter(Tenant.owner_id == current_user.id).first()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )
        
        # Get voice
        voice = await voice_service.get_voice_by_id(voice_id)
        
        if not voice:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Voice not found"
            )
        
        # Check if user can access premium voices
        if voice.is_premium and tenant.plan == "free":
            raise HTTPException(
                status_code=status.HTTP_402_PAYMENT_REQUIRED,
                detail="Premium voice requires paid plan"
            )
        
        return voice.dict()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to get voice {voice_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get voice: {str(e)}"
        )


@router.post("/test")
async def test_voice(
    request: VoiceTestRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Test a voice by generating a short audio sample.
    
    Returns audio URL for the test sample.
    """
    try:
        # Get user's tenant
        tenant = db.query(Tenant).filter(Tenant.owner_id == current_user.id).first()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )
        
        # Get voice and check access
        voice = await voice_service.get_voice_by_id(request.voice_id)
        if not voice:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Voice not found"
            )
        
        if voice.is_premium and tenant.plan == "free":
            raise HTTPException(
                status_code=status.HTTP_402_PAYMENT_REQUIRED,
                detail="Premium voice requires paid plan"
            )
        
        # Generate test audio
        tts_request = TTSRequest(
            text=request.text,
            voice_id=request.voice_id,
            speed=request.speed,
            pitch=request.pitch
        )
        
        result = await voice_service.synthesize_speech(tts_request)
        
        if result.success:
            return {
                "success": True,
                "audio_url": result.audio_url,
                "duration_seconds": result.duration_seconds,
                "voice_id": request.voice_id,
                "voice_name": voice.name
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Voice synthesis failed: {result.error_message}"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to test voice: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to test voice: {str(e)}"
        )


@router.post("/generate-script")
async def generate_script(
    request: ScriptGenerationRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Generate a script for product video narration.
    
    Returns AI-generated script based on product information.
    """
    try:
        # Get user's tenant
        tenant = db.query(Tenant).filter(Tenant.owner_id == current_user.id).first()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )
        
        # Generate script
        script = voice_service.generate_product_script(
            product_title=request.product_title,
            product_description=request.product_description,
            key_features=request.key_features,
            price=request.price,
            style=request.style
        )
        
        # Estimate speech duration (rough calculation)
        estimated_duration = len(script) * 0.08  # ~8 characters per second
        
        return {
            "script": script,
            "style": request.style,
            "estimated_duration_seconds": estimated_duration,
            "character_count": len(script),
            "word_count": len(script.split()),
            "product_title": request.product_title
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to generate script: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate script: {str(e)}"
        )


@router.get("/styles/list")
async def list_voice_styles(
    current_user: User = Depends(get_current_user)
):
    """
    List all available voice styles with descriptions.
    """
    try:
        styles = [
            {
                "style": VoiceStyle.CONVERSATIONAL.value,
                "name": "Conversational",
                "description": "Natural, everyday speaking style perfect for most content",
                "use_cases": ["Product descriptions", "Tutorials", "General content"]
            },
            {
                "style": VoiceStyle.PROFESSIONAL.value,
                "name": "Professional",
                "description": "Clear, authoritative tone ideal for business content",
                "use_cases": ["Corporate videos", "Training materials", "Presentations"]
            },
            {
                "style": VoiceStyle.ENERGETIC.value,
                "name": "Energetic",
                "description": "Dynamic, enthusiastic delivery for promotional content",
                "use_cases": ["Advertisements", "Product launches", "Sales videos"]
            },
            {
                "style": VoiceStyle.CALM.value,
                "name": "Calm",
                "description": "Soothing, relaxed tone for wellness and lifestyle content",
                "use_cases": ["Meditation", "Wellness products", "Lifestyle content"]
            },
            {
                "style": VoiceStyle.FRIENDLY.value,
                "name": "Friendly",
                "description": "Warm, approachable voice for customer-facing content",
                "use_cases": ["Customer service", "Welcome messages", "Social content"]
            },
            {
                "style": VoiceStyle.AUTHORITATIVE.value,
                "name": "Authoritative",
                "description": "Confident, expert tone for educational and technical content",
                "use_cases": ["Educational content", "Technical explanations", "Expert reviews"]
            }
        ]
        
        return {
            "styles": styles,
            "total_styles": len(styles)
        }
        
    except Exception as e:
        logger.exception(f"Failed to list voice styles: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list voice styles: {str(e)}"
        )


@router.get("/script-styles/list")
async def list_script_styles(
    current_user: User = Depends(get_current_user)
):
    """
    List all available script generation styles.
    """
    try:
        script_styles = [
            {
                "style": "promotional",
                "name": "Promotional",
                "description": "Exciting, sales-focused script to drive conversions",
                "tone": "Enthusiastic and persuasive",
                "example": "Introducing the amazing [Product]! Don't miss out - get yours today!"
            },
            {
                "style": "informational",
                "name": "Informational",
                "description": "Educational, fact-based script focusing on features and benefits",
                "tone": "Clear and informative",
                "example": "Let me tell you about [Product]. This product offers [features]."
            },
            {
                "style": "testimonial",
                "name": "Testimonial",
                "description": "Personal, authentic script written from customer perspective",
                "tone": "Personal and authentic",
                "example": "I've been using [Product] and I have to say, it's incredible."
            }
        ]
        
        return {
            "script_styles": script_styles,
            "total_styles": len(script_styles)
        }
        
    except Exception as e:
        logger.exception(f"Failed to list script styles: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list script styles: {str(e)}"
        )


@router.get("/provider/info")
async def get_provider_info(
    current_user: User = Depends(get_current_user)
):
    """
    Get information about the current TTS provider.
    """
    try:
        provider_info = voice_service.get_provider_info()
        
        return {
            "provider_info": provider_info,
            "timestamp": "2025-01-04T14:30:00Z"
        }
        
    except Exception as e:
        logger.exception(f"Failed to get provider info: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get provider info: {str(e)}"
        )
