import logging
from enum import Enum
from typing import Dict, List, Optional, Any

from pydantic import BaseModel

logger = logging.getLogger(__name__)

class VoiceGender(str, Enum):
    MALE = "male"
    FEMALE = "female"
    NEUTRAL = "neutral"

class VoiceStyle(str, Enum):
    CONVERSATIONAL = "conversational"
    PROFESSIONAL = "professional"
    ENERGETIC = "energetic"
    CALM = "calm"
    FRIENDLY = "friendly"
    AUTHORITATIVE = "authoritative"

class VoiceProvider(str, Enum):
    MOCK = "mock"
    # Add other providers here

class TTSRequest(BaseModel):
    text: str
    voice_id: str
    speed: float = 1.0
    pitch: float = 1.0

class TTSResult(BaseModel):
    success: bool
    audio_url: Optional[str] = None
    duration_seconds: Optional[float] = None
    error_message: Optional[str] = None

class VoiceInfo(BaseModel):
    id: str
    name: str
    provider: str
    provider_voice_id: str
    gender: Optional[VoiceGender] = None
    accent: Optional[str] = None
    language: str
    sample_url: Optional[str] = None
    description: Optional[str] = None
    is_active: bool
    is_premium: bool # Added is_premium for plan tier checks

class VoiceService:
    def __init__(self, provider: VoiceProvider = VoiceProvider.MOCK):
        self.provider = provider
        logger.info(f"Initialized mock VoiceService with provider: {provider}")

    async def get_available_voices(self, language: Optional[str] = None, gender: Optional[VoiceGender] = None, style: Optional[VoiceStyle] = None, include_premium: bool = True) -> List[VoiceInfo]:
        # Mock implementation
        voices = [
            VoiceInfo(id="mock-male-1", name="Mock Male Voice 1", provider="mock", provider_voice_id="m1", gender=VoiceGender.MALE, language="en", is_active=True, is_premium=False),
            VoiceInfo(id="mock-female-1", name="Mock Female Voice 1", provider="mock", provider_voice_id="f1", gender=VoiceGender.FEMALE, language="en", is_active=True, is_premium=False),
            VoiceInfo(id="mock-premium-1", name="Mock Premium Voice", provider="mock", provider_voice_id="p1", gender=VoiceGender.NEUTRAL, language="en", is_active=True, is_premium=True),
        ]
        filtered_voices = []
        for voice in voices:
            if language and voice.language != language: continue
            if gender and voice.gender != gender: continue
            if not include_premium and voice.is_premium: continue
            # Style filtering is more complex, mock it for now
            filtered_voices.append(voice)
        return filtered_voices

    async def get_voice_by_id(self, voice_id: str) -> Optional[VoiceInfo]:
        # Mock implementation
        for voice in await self.get_available_voices(include_premium=True):
            if voice.id == voice_id:
                return voice
        return None

    async def synthesize_speech(self, request: TTSRequest) -> TTSResult:
        # Mock implementation
        logger.info(f"Mock synthesizing speech for voice {request.voice_id}: {request.text[:30]}...")
        return TTSResult(success=True, audio_url=f"https://mock-audio.com/{request.voice_id}-{hash(request.text)}.mp3", duration_seconds=len(request.text) * 0.08)

    def generate_product_script(self, product_title: str, product_description: Optional[str] = None, key_features: Optional[List[str]] = None, price: Optional[float] = None, style: str = "promotional") -> str:
        # Mock implementation
        script = f"Introducing {product_title}! "
        if product_description: script += f"{product_description[:50]}... "
        if key_features: script += f"Key features include {', '.join(key_features)}. "
        if price: script += f"Available now for just ${price}. "
        script += f"This is a {style} script."
        return script

    def get_provider_info(self) -> Dict[str, Any]:
        return {
            "provider_name": self.provider.value,
            "supported_languages": ["en", "es"],
            "supported_genders": ["male", "female", "neutral"],
            "supported_styles": [s.value for s in VoiceStyle],
            "rate_limits": {"tts_requests_per_minute": 60}
        }
