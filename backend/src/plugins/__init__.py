"""
Plugin system for auto-discovering and loading plugin routes.
Enhanced with generic service interfaces for platform-agnostic operations.
"""

from fastapi import FastAPI

from .interfaces import MediaServiceInterface, SyncServiceInterface
from .media_service import PluginMediaService
from .plugin_manager import PluginManager

# Re-export interfaces for convenience
__all__ = [
    'MediaServiceInterface',
    'SyncServiceInterface',
    'PluginMediaService',
    'PluginManager',
    'register_plugins',
    'get_media_service'
]

# Re-export Shopify service for direct use (following the pattern)
try:
    from .shopify.shopify_service import ShopifyGraphQLService
    __all__.append('ShopifyGraphQLService')
except ImportError:
    pass

# Global instances
plugin_manager = PluginManager()
plugin_media_service = PluginMediaService()


def register_plugins(app: FastAPI):
    """Convenience function to register all plugins with the FastAPI app."""
    plugin_manager.register_plugin_routes(app)


def get_media_service() -> PluginMediaService:
    """Get the generic media service instance."""
    return plugin_media_service