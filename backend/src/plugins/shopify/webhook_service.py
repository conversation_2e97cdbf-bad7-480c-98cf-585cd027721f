"""
Shopify webhook management service for ProductVideo platform.
Handles webhook subscriptions, verification, and processing for essential events.
Enhanced with robust error handling, logging, and retry mechanisms.
"""

import hashlib
import hmac
import json
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from enum import Enum

import httpx
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_, or_, func

from core.config import get_settings
from modules.stores.models import Store
# Import WebhookEvent from admin models
from modules.admin.models import WebhookEvent

logger = logging.getLogger(__name__)
settings = get_settings()


class WebhookProcessingStatus(str, Enum):
    """Webhook processing status."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRYING = "retrying"


class WebhookRetryService:
    """Service for handling webhook retry logic."""

    def __init__(self):
        self.max_retries = 3
        self.retry_delays = [30, 300, 1800]  # 30s, 5min, 30min

    async def should_retry(self, webhook_event: WebhookEvent) -> bool:
        """Determine if webhook should be retried."""
        return webhook_event.retry_count < webhook_event.max_retries

    async def get_retry_delay(self, retry_count: int) -> int:
        """Get delay in seconds for retry attempt."""
        if retry_count < len(self.retry_delays):
            return self.retry_delays[retry_count]
        return self.retry_delays[-1]  # Use last delay for additional retries

    async def schedule_retry(self, webhook_event: WebhookEvent, db: AsyncSession):
        """Schedule webhook for retry."""
        if not await self.should_retry(webhook_event):
            logger.error(f"Max retries exceeded for webhook {webhook_event.event_id}")
            return

        delay = await self.get_retry_delay(webhook_event.retry_count)
        retry_at = datetime.utcnow() + timedelta(seconds=delay)

        webhook_event.status = WebhookProcessingStatus.RETRYING.value
        webhook_event.retry_count += 1
        webhook_event.updated_at = datetime.utcnow()

        await db.commit()

        logger.info(f"Scheduled retry for webhook {webhook_event.event_id} in {delay} seconds")

        # Schedule the actual retry (in a real implementation, this would use a task queue)
        asyncio.create_task(self._execute_retry(webhook_event, db, delay))

    async def _execute_retry(self, webhook_event: WebhookEvent, db: AsyncSession, delay: int):
        """Execute the retry after delay."""
        await asyncio.sleep(delay)

        # Refresh the webhook event from database
        result = await db.execute(
            select(WebhookEvent).where(WebhookEvent.id == webhook_event.id)
        )
        current_event = result.scalar_one_or_none()

        if current_event and current_event.status == WebhookProcessingStatus.RETRYING.value:
            # Reset status to pending for reprocessing
            current_event.status = WebhookProcessingStatus.PENDING.value
            await db.commit()

            logger.info(f"Webhook {webhook_event.event_id} ready for retry")


# Global retry service instance
webhook_retry_service = WebhookRetryService()


class ShopifyWebhookService:
    """
    Manages Shopify webhooks for ProductVideo platform.
    Handles subscription, verification, and processing of essential webhook events.
    """
    
    # Essential webhook topics for ProductVideo
    WEBHOOK_TOPICS = [
        "products/create",
        "products/update", 
        "products/delete",
        "orders/create",
        "orders/updated",
        "app/uninstalled",
        "shop/update"
    ]
    
    def __init__(self, store: Store):
        self.store = store
        self.api_version = "2025-07"
        self.webhook_endpoint = f"https://{store.shop_domain}/admin/api/{self.api_version}/webhooks.json"
        
        if not store.admin_access_token:
            raise ValueError("Store must have admin access token for webhook management")
            
        self.headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": store.admin_access_token,
        }
        
        # Base URL for webhook endpoints
        self.webhook_base_url = f"{settings.BASE_URL}/api/webhooks/shopify"
        
        logger.info(f"Initialized webhook service for store: {store.shop_domain}")
    
    async def subscribe_to_webhooks(self) -> Dict[str, any]:
        """
        Subscribe to all essential webhooks for ProductVideo platform.
        
        Returns:
            Dict with subscription results
        """
        results = {
            "success": [],
            "failed": [],
            "already_exists": []
        }
        
        async with httpx.AsyncClient() as client:
            # Get existing webhooks first
            existing_webhooks = await self._get_existing_webhooks(client)
            existing_topics = {wh["topic"] for wh in existing_webhooks}
            
            for topic in self.WEBHOOK_TOPICS:
                if topic in existing_topics:
                    results["already_exists"].append(topic)
                    continue
                
                webhook_url = f"{self.webhook_base_url}/{topic.replace('/', '_')}"
                
                webhook_data = {
                    "webhook": {
                        "topic": topic,
                        "address": webhook_url,
                        "format": "json"
                    }
                }
                
                try:
                    response = await client.post(
                        self.webhook_endpoint,
                        headers=self.headers,
                        json=webhook_data
                    )
                    
                    if response.status_code == 201:
                        webhook = response.json()["webhook"]
                        results["success"].append({
                            "topic": topic,
                            "id": webhook["id"],
                            "address": webhook["address"]
                        })
                        logger.info(f"Successfully subscribed to webhook: {topic}")
                    else:
                        error_msg = f"Failed to subscribe to {topic}: {response.status_code}"
                        results["failed"].append({"topic": topic, "error": error_msg})
                        logger.error(error_msg)
                        
                except Exception as e:
                    error_msg = f"Exception subscribing to {topic}: {str(e)}"
                    results["failed"].append({"topic": topic, "error": error_msg})
                    logger.error(error_msg)
        
        logger.info(f"Webhook subscription completed: {len(results['success'])} success, {len(results['failed'])} failed")
        return results
    
    async def _get_existing_webhooks(self, client: httpx.AsyncClient) -> List[Dict]:
        """Get existing webhooks for the store."""
        try:
            response = await client.get(self.webhook_endpoint, headers=self.headers)
            if response.status_code == 200:
                return response.json().get("webhooks", [])
        except Exception as e:
            logger.error(f"Failed to get existing webhooks: {e}")
        
        return []
    
    async def unsubscribe_from_webhooks(self) -> Dict[str, any]:
        """
        Unsubscribe from all webhooks (used during app uninstall).
        
        Returns:
            Dict with unsubscription results
        """
        results = {"success": [], "failed": []}
        
        async with httpx.AsyncClient() as client:
            existing_webhooks = await self._get_existing_webhooks(client)
            
            for webhook in existing_webhooks:
                webhook_id = webhook["id"]
                delete_url = f"https://{self.store.shop_domain}/admin/api/{self.api_version}/webhooks/{webhook_id}.json"
                
                try:
                    response = await client.delete(delete_url, headers=self.headers)
                    
                    if response.status_code == 200:
                        results["success"].append({
                            "id": webhook_id,
                            "topic": webhook["topic"]
                        })
                        logger.info(f"Successfully unsubscribed from webhook: {webhook['topic']}")
                    else:
                        error_msg = f"Failed to unsubscribe from webhook {webhook_id}: {response.status_code}"
                        results["failed"].append({"id": webhook_id, "error": error_msg})
                        logger.error(error_msg)
                        
                except Exception as e:
                    error_msg = f"Exception unsubscribing from webhook {webhook_id}: {str(e)}"
                    results["failed"].append({"id": webhook_id, "error": error_msg})
                    logger.error(error_msg)
        
        return results
    
    def verify_webhook_signature(self, payload: bytes, signature: str) -> bool:
        """
        Verify Shopify webhook signature using HMAC-SHA256.
        
        Args:
            payload: Raw webhook payload
            signature: X-Shopify-Hmac-Sha256 header value
            
        Returns:
            True if signature is valid, False otherwise
        """
        if not settings.SHOPIFY_API_SECRET:
            logger.error("Shopify API secret not configured for webhook verification")
            return False
        
        try:
            # Calculate expected signature
            expected_signature = hmac.new(
                settings.SHOPIFY_API_SECRET.encode('utf-8'),
                payload,
                hashlib.sha256
            ).hexdigest()
            
            # Compare signatures
            return hmac.compare_digest(signature, expected_signature)
            
        except Exception as e:
            logger.error(f"Error verifying webhook signature: {e}")
            return False
    
    async def process_webhook(
        self,
        topic: str,
        payload: Dict,
        db: AsyncSession,
        event_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Process incoming webhook based on topic with enhanced error handling and retry logic.

        Args:
            topic: Webhook topic (e.g., 'products/create')
            payload: Webhook payload
            db: Database session
            event_id: Optional webhook event ID for tracking

        Returns:
            Processing result
        """
        start_time = datetime.utcnow()
        logger.info(f"Processing webhook: {topic} for shop {self.store.shop_domain}")

        # Create or get webhook event record
        webhook_event = await self._get_or_create_webhook_event(db, event_id, topic, payload)

        try:
            # Update status to processing
            webhook_event.status = WebhookProcessingStatus.PROCESSING.value
            webhook_event.processing_started_at = start_time
            await db.commit()

            # Process based on topic
            result = await self._process_webhook_by_topic(topic, payload, db)

            # Mark as completed
            webhook_event.status = WebhookProcessingStatus.COMPLETED.value
            webhook_event.completed_at = datetime.utcnow()
            await db.commit()

            processing_time = (datetime.utcnow() - start_time).total_seconds()
            logger.info(f"Webhook {topic} processed successfully in {processing_time:.2f}s")

            return result

        except Exception as e:
            error_msg = f"Error processing webhook {topic}: {str(e)}"
            logger.error(error_msg, exc_info=True)

            # Update webhook event with error
            webhook_event.status = WebhookProcessingStatus.FAILED.value
            webhook_event.last_error = error_msg
            await db.commit()

            # Schedule retry if applicable
            if await webhook_retry_service.should_retry(webhook_event):
                await webhook_retry_service.schedule_retry(webhook_event, db)

            return {"status": "error", "error": error_msg, "will_retry": await webhook_retry_service.should_retry(webhook_event)}

    async def _get_or_create_webhook_event(
        self,
        db: AsyncSession,
        event_id: Optional[str],
        topic: str,
        payload: Dict
    ) -> WebhookEvent:
        """Get existing webhook event or create new one."""
        if event_id:
            result = await db.execute(
                select(WebhookEvent).where(WebhookEvent.event_id == event_id)
            )
            webhook_event = result.scalar_one_or_none()
            if webhook_event:
                return webhook_event

        # Create new webhook event
        webhook_event = WebhookEvent(
            event_id=event_id or f"{topic}_{datetime.utcnow().timestamp()}",
            topic=topic,
            shop_domain=self.store.shop_domain,
            payload=payload,
            status=WebhookProcessingStatus.PENDING.value
        )

        db.add(webhook_event)
        await db.commit()
        await db.refresh(webhook_event)

        return webhook_event

    async def _process_webhook_by_topic(
        self,
        topic: str,
        payload: Dict,
        db: AsyncSession
    ) -> Dict[str, Any]:
        """Process webhook based on topic with enhanced error handling."""
        try:
            if topic == "products/create":
                return await self._handle_product_create(payload, db)
            elif topic == "products/update":
                return await self._handle_product_update(payload, db)
            elif topic == "products/delete":
                return await self._handle_product_delete(payload, db)
            elif topic == "orders/create":
                return await self._handle_order_create(payload, db)
            elif topic == "orders/updated":
                return await self._handle_order_update(payload, db)
            elif topic == "app/uninstalled":
                return await self._handle_app_uninstall(payload, db)
            elif topic == "shop/update":
                return await self._handle_shop_update(payload, db)
            else:
                logger.warning(f"Unhandled webhook topic: {topic}")
                return {"status": "ignored", "reason": "unhandled_topic"}

        except Exception as e:
            logger.error(f"Error in topic-specific handler for {topic}: {e}", exc_info=True)
            raise

    async def _trigger_video_generation(self, product_id: str, db: AsyncSession):
        """Trigger video generation for a product."""
        try:
            from modules.media_generation.service import media_generation_service
            from modules.media_generation.schemas import MediaGenerateRequest

            # Create video generation request
            request = MediaGenerateRequest(
                shop_id=self.store.id,
                product_ids=[product_id],
                media_type="video",
                template_id="default",
                voice_id="default",
                aspect_ratio="16:9",
                text_input=f"Product {product_id} - Amazing features and benefits"
            )

            # Get tenant ID (assuming store has tenant relationship)
            tenant_id = getattr(self.store, 'tenant_id', 1)

            # Create generation jobs
            jobs = await media_generation_service.create_generation_jobs(
                db=db,
                user_id=tenant_id,  # Using tenant_id as user_id for now
                request=request
            )

            logger.info(f"Triggered video generation for product {product_id}: {len(jobs)} jobs created")

        except Exception as e:
            logger.error(f"Error triggering video generation for product {product_id}: {e}", exc_info=True)
            # Don't raise here - we don't want video generation failure to fail the webhook
    
    async def _handle_product_create(self, payload: Dict, db: AsyncSession) -> Dict:
        """Handle product creation webhook with product sync and video generation."""
        product_id = str(payload["id"])
        logger.info(f"Processing product creation: {product_id}")

        try:
            # Sync product to database
            from plugins import ShopifyGraphQLService, StoreSyncService

            store_service = ShopifyGraphQLService(
                shop_domain=self.store.shop_domain,
                admin_access_token=self.store.admin_access_token,
                storefront_access_token=self.store.storefront_access_token
            )
            sync_service = StoreSyncService(store_service, self.store.shop_domain)
            # Sync the specific product
            sync_result = await sync_service.sync_products(
                db=db,
                limit=1,
                cursor=None  # Use cursor instead of since_id
            )

            if sync_result.get("added", 0) > 0:
                logger.info(f"Successfully synced product {product_id}")

                # Trigger video generation for the new product
                await self._trigger_video_generation(product_id, db)

                return {
                    "status": "processed",
                    "action": "product_created_and_synced",
                    "product_id": product_id,
                    "sync_result": sync_result,
                    "video_generation_triggered": True
                }
            else:
                logger.warning(f"Product {product_id} was not synced")
                return {
                    "status": "processed",
                    "action": "product_created_no_sync",
                    "product_id": product_id
                }

        except Exception as e:
            logger.error(f"Error handling product creation for {product_id}: {e}", exc_info=True)
            raise
    
    async def _handle_product_update(self, payload: Dict, db: AsyncSession) -> Dict:
        """Handle product update webhook with data sync and video regeneration."""
        product_id = str(payload["id"])
        logger.info(f"Processing product update: {product_id}")

        try:
            # Sync updated product data
            from plugins import ShopifyGraphQLService, StoreSyncService

            store_service = ShopifyGraphQLService(
                shop_domain=self.store.shop_domain,
                admin_access_token=self.store.admin_access_token,
                storefront_access_token=self.store.storefront_access_token
            )
            sync_service = StoreSyncService(store_service, self.store.shop_domain)
            # Sync the specific product
            sync_result = await sync_service.sync_products(
                db=db,
                limit=1,
                cursor=None
            )

            if sync_result.get("updated", 0) > 0:
                logger.info(f"Successfully updated product {product_id}")

                # Check if significant changes warrant video regeneration
                if self._should_regenerate_video(payload):
                    await self._trigger_video_regeneration(product_id, db)
                    return {
                        "status": "processed",
                        "action": "product_updated_and_regenerated",
                        "product_id": product_id,
                        "sync_result": sync_result,
                        "video_regeneration_triggered": True
                    }
                else:
                    return {
                        "status": "processed",
                        "action": "product_updated_no_regeneration",
                        "product_id": product_id,
                        "sync_result": sync_result
                    }
            else:
                logger.info(f"No changes detected for product {product_id}")
                return {
                    "status": "processed",
                    "action": "product_updated_no_changes",
                    "product_id": product_id
                }

        except Exception as e:
            logger.error(f"Error handling product update for {product_id}: {e}", exc_info=True)
            raise

    def _should_regenerate_video(self, payload: Dict) -> bool:
        """Determine if product changes warrant video regeneration."""
        # Check for significant changes that would affect video content
        significant_fields = ['title', 'body_html', 'images', 'variants']

        for field in significant_fields:
            if field in payload.get('updated_fields', []):
                return True

        # Check if images were added/removed
        if 'images' in payload:
            return True

        return False

    async def _trigger_video_regeneration(self, product_id: str, db: AsyncSession):
        """Trigger video regeneration for an updated product."""
        try:
            from modules.media_generation.service import media_generation_service

            # Find existing video variants for this product
            from modules.media_generation.models import MediaVariant

            result = await db.execute(
                select(MediaVariant).where(MediaVariant.product_id == product_id)
            )
            variants = result.scalars().all()

            if variants:
                # Regenerate the first variant as an example
                await media_generation_service.regenerate_variant(
                    job_id=variants[0].job_id,
                    variant_id=variants[0].id,
                    override_params={"reason": "product_update"}
                )
                logger.info(f"Triggered video regeneration for product {product_id}")
            else:
                # No existing variants, trigger new generation
                await self._trigger_video_generation(product_id, db)

        except Exception as e:
            logger.error(f"Error triggering video regeneration for product {product_id}: {e}", exc_info=True)

    async def _track_order_conversion(self, payload: Dict, db: AsyncSession):
        """Track order conversion for analytics."""
        try:
            from modules.analytics.event_service import analytics_event_service
            from modules.analytics.event_models import EventType

            order_id = str(payload["id"])
            order_value = float(payload.get("total_price", 0))
            currency = payload.get("currency", "USD")

            # Extract product IDs from line items
            product_ids = []
            for line_item in payload.get("line_items", []):
                if line_item.get("product_id"):
                    product_ids.append(str(line_item["product_id"]))

            # Create purchase events for each product
            for product_id in product_ids:
                event_request = {
                    "event_type": EventType.PURCHASE,
                    "session_id": f"order_{order_id}",
                    "user_id": payload.get("customer", {}).get("id"),
                    "product_id": product_id,
                    "order_id": order_id,
                    "order_value": order_value,
                    "currency": currency,
                    "timestamp": datetime.utcnow(),
                    "properties": {
                        "order_number": payload.get("order_number"),
                        "line_items_count": len(payload.get("line_items", []))
                    }
                }

                # Get tenant ID
                tenant_id = getattr(self.store, 'tenant_id', 1)

                # Ingest the event (this will also update conversion funnels)
                await analytics_event_service.ingest_event(
                    db=db,
                    tenant_id=tenant_id,
                    event_request=event_request
                )

            logger.info(f"Tracked conversion for order {order_id} with {len(product_ids)} products")

        except Exception as e:
            logger.error(f"Error tracking conversion for order {order_id}: {e}", exc_info=True)
    
    async def _handle_product_delete(self, payload: Dict, db: AsyncSession) -> Dict:
        """Handle product deletion webhook with cleanup."""
        product_id = str(payload["id"])
        logger.info(f"Processing product deletion: {product_id}")

        try:
            # Clean up associated videos and variants
            from modules.media_generation.models import MediaVariant, MediaJob

            # Find all variants for this product
            result = await db.execute(
                select(MediaVariant).where(MediaVariant.product_id == product_id)
            )
            variants = result.scalars().all()

            deleted_variants = 0
            deleted_jobs = 0

            for variant in variants:
                # Delete associated files from storage
                await self._cleanup_variant_files(variant, db)

                # Delete the variant
                await db.delete(variant)
                deleted_variants += 1

                # Check if job has no more variants
                job_result = await db.execute(
                    select(MediaJob).where(MediaJob.id == variant.job_id)
                )
                job = job_result.scalar_one_or_none()

                if job:
                    variants_count_result = await db.execute(
                        select(func.count(MediaVariant.id)).where(MediaVariant.job_id == job.id)
                    )
                    remaining_variants = variants_count_result.scalar()

                    if remaining_variants == 0:
                        await db.delete(job)
                        deleted_jobs += 1

            # Remove product from database
            from modules.products.models import Product
            result = await db.execute(
                select(Product).where(Product.external_id == product_id)
            )
            product = result.scalar_one_or_none()

            if product:
                await db.delete(product)

            await db.commit()

            logger.info(f"Cleaned up product {product_id}: {deleted_variants} variants, {deleted_jobs} jobs")

            return {
                "status": "processed",
                "action": "product_deleted_and_cleaned",
                "product_id": product_id,
                "deleted_variants": deleted_variants,
                "deleted_jobs": deleted_jobs
            }

        except Exception as e:
            logger.error(f"Error handling product deletion for {product_id}: {e}", exc_info=True)
            raise

    async def _cleanup_variant_files(self, variant: Any, db: AsyncSession):
        """Clean up files associated with a variant."""
        try:
            from modules.media_generation.storage_service import media_storage_service

            files_to_delete = []

            if variant.video_url:
                # Extract key from URL
                video_key = variant.video_url.split('/')[-1]
                files_to_delete.append(video_key)

            if variant.image_url:
                image_key = variant.image_url.split('/')[-1]
                files_to_delete.append(image_key)

            if variant.voice_url:
                voice_key = variant.voice_url.split('/')[-1]
                files_to_delete.append(voice_key)

            # Delete files from storage
            for file_key in files_to_delete:
                try:
                    await media_storage_service.delete_file(file_key)
                    logger.info(f"Deleted file: {file_key}")
                except Exception as e:
                    logger.warning(f"Failed to delete file {file_key}: {e}")

        except Exception as e:
            logger.error(f"Error cleaning up variant files: {e}", exc_info=True)
    
    async def _handle_order_create(self, payload: Dict, db: AsyncSession) -> Dict:
        """Handle order creation webhook with conversion tracking."""
        order_id = str(payload["id"])
        logger.info(f"Processing order creation: {order_id}")

        try:
            # Sync order to database
            from plugins import ShopifyGraphQLService, StoreSyncService

            store_service = ShopifyGraphQLService(
                shop_domain=self.store.shop_domain,
                admin_access_token=self.store.admin_access_token,
                storefront_access_token=self.store.storefront_access_token
            )
            sync_service = StoreSyncService(store_service, self.store.shop_domain)
            # Sync orders
            sync_result = await sync_service.sync_orders(db=db, limit=1)

            if sync_result.get("added", 0) > 0:
                logger.info(f"Successfully synced order {order_id}")

                # Track conversion for analytics
                await self._track_order_conversion(payload, db)

                return {
                    "status": "processed",
                    "action": "order_created_and_synced",
                    "order_id": order_id,
                    "sync_result": sync_result,
                    "conversion_tracked": True
                }
            else:
                return {
                    "status": "processed",
                    "action": "order_created_no_sync",
                    "order_id": order_id
                }

        except Exception as e:
            logger.error(f"Error handling order creation for {order_id}: {e}", exc_info=True)
            raise
    
    async def _handle_order_update(self, payload: Dict, db: AsyncSession) -> Dict:
        """Handle order update webhook with analytics tracking."""
        order_id = str(payload["id"])
        logger.info(f"Processing order update: {order_id}")

        try:
            # Sync updated order data
            from plugins import ShopifyGraphQLService, StoreSyncService

            store_service = ShopifyGraphQLService(
                shop_domain=self.store.shop_domain,
                admin_access_token=self.store.admin_access_token,
                storefront_access_token=self.store.storefront_access_token
            )
            sync_service = StoreSyncService(store_service, self.store.shop_domain)
            sync_result = await sync_service.sync_orders(db=db, limit=1)

            # Update conversion tracking if order status changed
            if payload.get("financial_status") == "paid":
                await self._track_order_conversion(payload, db)

            return {
                "status": "processed",
                "action": "order_updated",
                "order_id": order_id,
                "sync_result": sync_result
            }

        except Exception as e:
            logger.error(f"Error handling order update for {order_id}: {e}", exc_info=True)
            raise

    async def _handle_app_uninstall(self, payload: Dict, db: AsyncSession) -> Dict:
        """Handle app uninstall webhook with cleanup."""
        shop_domain = payload.get("domain", self.store.shop_domain)
        logger.info(f"Processing app uninstall from shop: {shop_domain}")

        try:
            # Deactivate the store
            self.store.is_active = False
            self.store.updated_at = datetime.utcnow()

            # Clean up webhooks
            await self.unsubscribe_from_webhooks()

            # Mark store as deactivated
            await db.commit()

            logger.info(f"Successfully processed app uninstall for shop: {shop_domain}")

            return {
                "status": "processed",
                "action": "app_uninstalled_and_cleaned",
                "shop": shop_domain,
                "store_deactivated": True
            }

        except Exception as e:
            logger.error(f"Error handling app uninstall for {shop_domain}: {e}", exc_info=True)
            raise

    async def _handle_shop_update(self, payload: Dict, db: AsyncSession) -> Dict:
        """Handle shop update webhook with data sync."""
        shop_id = str(payload["id"])
        logger.info(f"Processing shop update: {shop_id}")

        try:
            # Update store information
            if payload.get("name"):
                self.store.shop_name = payload["name"]
            if payload.get("domain"):
                self.store.shop_domain = payload["domain"]

            self.store.updated_at = datetime.utcnow()
            await db.commit()

            logger.info(f"Successfully updated shop information for: {shop_id}")

            return {
                "status": "processed",
                "action": "shop_updated",
                "shop_id": shop_id,
                "updated_fields": ["name", "domain"]
            }

        except Exception as e:
            logger.error(f"Error handling shop update for {shop_id}: {e}", exc_info=True)
            raise
