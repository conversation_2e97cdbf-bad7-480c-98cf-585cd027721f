{"task_routes": {"media_generation.generate_media": {"queue": "media-generation"}, "media_push.push_to_shopify": {"queue": "media-push"}, "analytics.process_analytics": {"queue": "analytics-processing"}, "sync.sync_data": {"queue": "sync-jobs"}}, "task_default_queue": "celery", "task_default_exchange": "celery", "task_default_exchange_type": "direct", "task_default_routing_key": "celery", "worker_prefetch_multiplier": 1, "task_acks_late": true, "worker_max_tasks_per_child": 1000, "worker_disable_rate_limits": false, "worker_send_task_events": true, "task_send_sent_event": true, "result_expires": 3600, "result_backend_transport_options": {"retry_policy": {"timeout": 5.0}}, "broker_transport_options": {"retry_policy": {"timeout": 5.0}}}