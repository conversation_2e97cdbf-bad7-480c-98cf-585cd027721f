"""
Main entry point for Celery worker.
Replaces BullMQ with Celery for distributed task processing.
"""

import logging
import os
import sys
from pathlib import Path

# Add src to path for absolute imports
current_dir = Path(__file__).parent
src_dir = current_dir.parent
sys.path.insert(0, str(src_dir))

from core.config import get_settings

# Get settings first
settings = get_settings()

from celery import Celery
from celery.signals import worker_ready, worker_shutdown

from servers.worker.processors import (
    MediaGenerationProcessor,
    MediaPushProcessor,
    AnalyticsProcessor,
    SyncProcessor
)

logger = logging.getLogger(__name__)

# Create Celery app
celery_app = Celery(
    'productvideo_worker',
    broker=settings.REDIS_URL,
    backend=settings.REDIS_URL,
    include=['servers.worker.tasks']
)

# Load configuration from JSON
import json
config_path = Path(__file__).parent / "config.json"
with open(config_path) as f:
    celery_config = json.load(f)

celery_app.conf.update(celery_config)

# Celery signal handlers
@worker_ready.connect
def worker_ready_handler(sender, **kwargs):
    """Called when Celery worker is ready."""
    logger.info(f"Celery worker {sender.hostname} is ready")

@worker_shutdown.connect
def worker_shutdown_handler(sender, **kwargs):
    """Called when Celery worker shuts down."""
    logger.info(f"Celery worker {sender.hostname} is shutting down")

# Import tasks to register them with Celery
try:
    from . import tasks
    logger.info("Celery tasks imported successfully")
except ImportError as e:
    logger.error(f"Failed to import Celery tasks: {e}")
    raise


if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Celery worker is now started via docker-compose
    print("Celery worker should be started with: docker-compose up worker")
    print("Or manually with: celery -A servers.worker.main worker --loglevel=info")
