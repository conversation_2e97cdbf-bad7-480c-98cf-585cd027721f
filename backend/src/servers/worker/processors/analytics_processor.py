import logging
from typing import Dict, Any


from core.db.database import get_db
from modules.analytics.event_service import analytics_event_service

logger = logging.getLogger(__name__)


class AnalyticsProcessor:
    def __init__(self):
        pass

    def process(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process analytics event.

        Args:
            job_data: Job data dictionary

        Returns:
            Job result
        """
        import asyncio
        return asyncio.run(self._process_async(job_data))

    async def _process_async(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Async implementation of process method.
        """
        tenant_id = job_data["tenant_id"]
        event_data = job_data["event_data"]

        logger.info(f"Processing analytics job for tenant {tenant_id}")

        try:
            async for db in get_db():
                # Process the analytics event
                from modules.analytics.event_schemas import EventIngestionRequest

                event_request = EventIngestionRequest(**event_data)

                response = await analytics_event_service.ingest_event(
                    db, tenant_id, event_request
                )

                logger.info(f"Analytics job completed: {response.status}")

                return {
                    "success": True,
                    "event_id": response.event_id,
                    "status": response.status
                }

        except Exception as e:
            logger.error(f"Analytics job failed: {e}")
            raise e