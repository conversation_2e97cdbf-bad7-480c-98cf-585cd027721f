import asyncio
import logging
import os
from typing import Dict, Any, Optional
from datetime import datetime

# Celery doesn't need specific imports for job handling

from core.config import get_settings
from core.db.database import get_db
from modules.media_generation.models import MediaJob, MediaVariant, MediaJobStatus, MediaVariantStatus
from modules.media_generation.image_service import ai_image_service
from modules.media_generation.video_service import ai_video_service
from modules.media_generation.voice_service import ai_voice_service
from modules.media_generation.schemas import MediaGenerationRequest, MediaGenerationResult
from modules.media_generation.storage_service import media_storage_service
from modules.media_generation.transcoding_service import video_transcoding_service
from modules.billing.service import billing_service

logger = logging.getLogger(__name__)
settings = get_settings()

class MediaGenerationProcessor:
    def __init__(self):
        pass

    def process(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process video generation job.

        Args:
            job_data: Job data dictionary

        Returns:
            Job result
        """
        # Run async operations in event loop
        return asyncio.run(self._process_async(job_data))

    async def _process_async(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Async implementation of process method.
        """
        tenant_id = job_data["tenant_id"]
        db_job_id = job_data["job_id"]
        product_ids = job_data["product_ids"]
        template_id = job_data["template_id"]
        voice_id = job_data["voice_id"]

        media_type = job_data["media_type"]
        logger.info(f"Processing {media_type} generation job for tenant {tenant_id}")

        try:
            async for db in get_db():
                # Get media job from database
                media_job = await db.get(MediaJob, db_job_id)
                if not media_job:
                    raise Exception(f"Media job {db_job_id} not found")

                # Update job status
                media_job.status = MediaJobStatus.PROCESSING
                await db.commit()

                # Job progress tracking removed for Celery

                # Process each product
                total_products = len(product_ids)
                for i, product_id in enumerate(product_ids):
                    await self._generate_media_for_product(
                        db, media_job, product_id, template_id, voice_id, media_type
                    )

                    # Progress tracking removed for Celery

                # Record billing usage
                await billing_service.record_video_generation_usage(
                    db, tenant_id, str(db_job_id), len(product_ids) * 4  # 4 variants per product
                )

                # Update job status to completed
                media_job.status = MediaJobStatus.COMPLETED
                media_job.completed_at = datetime.utcnow()
                await db.commit()

                # Final progress update removed for Celery

                logger.info(f"{media_type} generation job completed successfully")

                return {
                    "success": True,
                    "job_id": db_job_id,
                    "products_processed": len(product_ids),
                    "variants_generated": len(product_ids) * 4
                }

        except Exception as e:
            logger.error(f"{media_type} generation job failed: {e}")

            # Update database job status
            async for db in get_db():
                media_job = await db.get(MediaJob, db_job_id)
                if media_job:
                    media_job.status = MediaJobStatus.FAILED
                    media_job.error_message = str(e)
                    await db.commit()
                break

            raise e
    
    async def _generate_media_for_product(
        self,
        db,
        media_job: MediaJob,
        product_id: str,
        template_id: str,
        voice_id: str,
        media_type: str
    ):
        """Generate media variants for a single product."""
        logger.info(f"Generating {media_type} for product {product_id}")
        
        request = MediaGenerationRequest(
            media_type=media_type,
            product_id=product_id,
            template_id=template_id,
            voice_id=voice_id,
            script=media_job.script or f"Amazing product {product_id}",
            aspect_ratio="16:9",
            variants_count=4
        )
        
        if media_type == "video":
            result = await ai_video_service.generate(request)
        elif media_type == "image":
            result = await ai_image_service.generate(request)
        elif media_type == "voice":
            result = await ai_voice_service.generate(request)
        else:
            raise ValueError(f"Unsupported media type: {media_type}")
        
        if result.success:
            for i, variant_data in enumerate(result.variants):
                variant = MediaVariant(
                    job_id=media_job.id,
                    product_id=product_id,
                    variant_name=f"Variant {i+1}",
                    status=MediaVariantStatus.PROCESSING,
                    provider_media_id=variant_data.get("id"),
                    generation_params=variant_data
                )
                db.add(variant)
                await db.flush()
                
                media_url = variant_data.get("video_url") or variant_data.get("image_url") or variant_data.get("voice_url")
                if media_url:
                    try:
                        if media_type == "video":
                            processing_result = await video_transcoding_service.process_video(
                                video_url=media_url,
                                output_formats=['mp4', 'hls'],
                                generate_thumbnails=True,
                                generate_subtitles=True,
                                thumbnail_count=3
                            )
                            storage_results = await self._upload_processed_media(
                                media_job.tenant_id, variant.id, processing_result, media_type
                            )
                            variant.video_url = storage_results.get('mp4_url')
                            variant.hls_url = storage_results.get('hls_url')
                            variant.thumbnail_urls = storage_results.get('thumbnail_urls', [])
                            variant.subtitle_url = storage_results.get('subtitle_url')
                        elif media_type == "image":
                            # For images, directly upload to storage
                            image_content = await ai_image_service.download_media(media_url)
                            image_key = f"tenants/{media_job.tenant_id}/images/{variant.id}/image.jpg"
                            image_url = await media_storage_service.upload_file(image_content, image_key) # Assuming upload_file can take content directly
                            variant.image_url = image_url
                        elif media_type == "voice":
                            # For voice, directly upload to storage
                            voice_content = await ai_voice_service.download_media(media_url)
                            voice_key = f"tenants/{media_job.tenant_id}/voices/{variant.id}/voice.mp3"
                            voice_url = await media_storage_service.upload_file(voice_content, voice_key) # Assuming upload_file can take content directly
                            variant.voice_url = voice_url
                        
                        variant.status = MediaVariantStatus.READY
                        logger.info(f"Processed variant {variant.id} for product {product_id}")
                        
                    except Exception as e:
                        logger.error(f"Failed to process variant {variant.id}: {e}")
                        variant.status = MediaVariantStatus.FAILED
                        variant.error_message = str(e)
                else:
                    variant.status = MediaVariantStatus.FAILED
                    variant.error_message = "No media URL provided"
            
            await db.commit()
            logger.info(f"Generated and processed {len(result.variants)} variants for product {product_id}")
        else:
            raise Exception(f"{media_type} generation failed: {result.error_message}")
    
    async def _upload_processed_media(
        self, 
        tenant_id: int, 
        variant_id: int, 
        processing_result: dict,
        media_type: str
    ) -> dict:
        """Upload processed media files to storage."""
        storage_results = {}
        
        try:
            if media_type == "video":
                # Upload MP4 video
                if 'mp4' in processing_result['outputs']:
                    mp4_path = processing_result['outputs']['mp4']
                    mp4_key = f"tenants/{tenant_id}/videos/{variant_id}/video.mp4"
                    mp4_url = await media_storage_service.upload_file(mp4_path, mp4_key)
                    storage_results['mp4_url'] = mp4_url
                
                # Upload HLS playlist and segments
                if 'hls' in processing_result['outputs']:
                    hls_path = processing_result['outputs']['hls']
                    hls_dir = os.path.dirname(hls_path)
                    
                    # Upload all HLS files
                    hls_urls = []
                    for file_name in os.listdir(hls_dir):
                        file_path = os.path.join(hls_dir, file_name)
                        hls_key = f"tenants/{tenant_id}/videos/{variant_id}/hls/{file_name}"
                        hls_url = await media_storage_service.upload_file(file_path, hls_key)
                        hls_urls.append(hls_url)
                    
                    # The main playlist URL
                    playlist_key = f"tenants/{tenant_id}/videos/{variant_id}/hls/playlist.m3u8"
                    storage_results['hls_url'] = f"{media_storage_service.base_url}/{playlist_key}"
                
                # Upload thumbnails
                thumbnail_urls = []
                for i, thumb_path in enumerate(processing_result['thumbnails']):
                    thumb_key = f"tenants/{tenant_id}/videos/{variant_id}/thumbnails/thumb_{i+1}.jpg"
                    thumb_url = await media_storage_service.upload_file(thumb_path, thumb_key)
                    thumbnail_urls.append(thumb_url)
                storage_results['thumbnail_urls'] = thumbnail_urls
                
                # Upload subtitles
                if processing_result['subtitles']:
                    subtitle_path = processing_result['subtitles']
                    subtitle_key = f"tenants/{tenant_id}/videos/{variant_id}/subtitles.srt"
                    subtitle_url = await media_storage_service.upload_file(subtitle_path, subtitle_key)
                    storage_results['subtitle_url'] = subtitle_url
            elif media_type == "image":
                # Image processing results would be different, handle accordingly
                pass
            elif media_type == "voice":
                # Voice processing results would be different, handle accordingly
                pass
            
            return storage_results
            
        except Exception as e:
            logger.error(f"Failed to upload processed media files: {e}")
            raise
