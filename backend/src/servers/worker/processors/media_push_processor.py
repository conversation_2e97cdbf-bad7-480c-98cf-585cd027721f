import logging
from typing import Dict, Any
from datetime import datetime


from core.db.database import get_db
from modules.media_generation.models import MediaVariant
from plugins import get_media_service

logger = logging.getLogger(__name__)


class MediaPushProcessor:
    def __init__(self):
        pass

    def process(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process media push to Shopify job.

        Args:
            job_data: Job data dictionary

        Returns:
            Job result
        """
        import asyncio
        return asyncio.run(self._process_async(job_data))

    async def _process_async(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Async implementation of process method.
        """
        tenant_id = job_data["tenant_id"]
        variant_id = job_data["variant_id"]
        product_id = job_data["product_id"]
        shop_domain = job_data["shop_domain"]
        store_type = job_data.get("store_type", "shopify")  # Default to shopify for backward compatibility

        logger.info(f"Processing media push job for variant {variant_id} to {store_type}")

        try:
            # Get the generic media service
            media_service = get_media_service()

            async for db in get_db():
                # Get media variant
                variant = await db.get(MediaVariant, variant_id)
                if not variant:
                    raise Exception(f"Media variant {variant_id} not found")

                # Determine media URL based on type
                media_url = variant.video_url or variant.image_url or variant.voice_url
                if not media_url:
                    raise Exception(f"Media variant {variant_id} has no media URL")

                # Push to platform using generic service
                push_result = await media_service.push_media_to_product(
                    store_type=store_type,
                    shop_domain=shop_domain,
                    product_id=product_id,
                    media_url=media_url,
                    alt_text=f"Product media - {variant.variant_name}"
                )

                if push_result.get("success"):
                    # Update variant status
                    variant.media_id = push_result.get("media_id")
                    variant.pushed_at = datetime.utcnow()
                    await db.commit()

                    logger.info(f"Successfully pushed variant {variant_id} to platform")

                    return {
                        "success": True,
                        "variant_id": variant_id,
                        "media_id": push_result.get("media_id")
                    }
                else:
                    raise Exception(push_result.get("error", "Unknown error"))

        except Exception as e:
            logger.error(f"Media push job failed: {e}")
            raise e