import logging
from typing import Dict, Any


from core.db.database import get_db
from plugins.shopify.shopify_service import ShopifyGraphQLService
from modules.stores.sync_service import StoreSyncService

logger = logging.getLogger(__name__)


class SyncProcessor:
    def __init__(self):
        pass

    async def process_async(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Async process sync job - to be called from within an existing event loop.
        """
        return await self._process_async(job_data)

    async def _process_async(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Async implementation of process method.
        """
        from core.db.database import get_db_session_factory

        store_id = job_data.get("store_id")
        shop_domain = job_data.get("shop_domain")
        access_token = job_data.get("access_token")
        sync_type = job_data.get("sync_type", "unknown")

        logger.info(f"Processing sync job for store {store_id}, type: {sync_type}")

        if not shop_domain or not access_token:
            raise ValueError("Missing shop_domain or access_token in sync job data")

        try:
            # Create database session directly instead of using the async generator
            db_factory = get_db_session_factory()
            db = db_factory()

            try:
                # Initialize sync progress tracking (outside main transaction)
                from modules.stores.progress_service import ProgressService
                progress_service = ProgressService(db)

                # Check if sync can proceed (prevent duplicate/rushed syncs)
                can_start, reason = await progress_service.can_start_sync(
                    store_id=int(store_id),
                    sync_type=sync_type,
                    cooldown_hours=1  # 1 hour cooldown
                )

                if not can_start:
                    logger.warning(f"Sync prevented for store {store_id}, type {sync_type}: {reason}")
                    return {"success": False, "error": reason, "sync_type": sync_type}

                # Get total items count first
                store_service = ShopifyGraphQLService(
                    shop_domain=shop_domain,
                    admin_access_token=access_token
                )
                temp_sync_service = StoreSyncService(store_service, shop_domain)

                if sync_type == "products":
                    products_data = await temp_sync_service._paginate_all(temp_sync_service.store_service.get_products, "products")
                    total_items = len(products_data.get("edges", []))
                elif sync_type == "orders":
                    orders_data = await temp_sync_service._paginate_all(temp_sync_service.store_service.get_orders, "orders")
                    total_items = len(orders_data.get("edges", []))
                else:
                    total_items = 0

                # Create sync progress record in its own transaction
                async with db.begin():
                    sync_progress = await progress_service.create_sync_progress(
                        store_id=int(store_id),
                        sync_type=sync_type,
                        total_items=total_items
                    )

                store_service = ShopifyGraphQLService(
                    shop_domain=shop_domain,
                    admin_access_token=access_token
                )
                sync_service = StoreSyncService(store_service, shop_domain)
                    if sync_type == "products":
                        sync_stats = await self._sync_products_with_progress(
                            sync_service, db, progress_service, sync_progress.id, total_items
                        )
                        logger.info(f"Product sync for store {store_id} completed: {sync_stats}")
                    elif sync_type == "orders":
                        sync_stats = await self._sync_orders_with_progress(
                            sync_service, db, progress_service, sync_progress.id, total_items
                        )
                        logger.info(f"Order sync for store {store_id} completed: {sync_stats}")
                    # Add more sync types as needed
                    else:
                        raise ValueError(f"Unsupported sync type: {sync_type}")

                return {"success": True, "status": "completed", "sync_type": sync_type, "stats": sync_stats}
            finally:
                await db.close()

        except Exception as e:
            logger.error(f"Sync job for store {store_id} failed: {e}")
            raise e

    async def _sync_products_with_progress(
        self,
        sync_service,
        db,
        progress_service,
        sync_progress_id: int,
        total_items: int
    ) -> Dict[str, int]:
        """Sync products with progress tracking and batch commits."""

        # Get products data
        response = await sync_service._paginate_all(sync_service.store_service.get_products, "products")
        products_data = response.get("edges", [])

        stats = {"added": 0, "updated": 0, "unchanged": 0}
        batch_size = 50
        current_batch = 0

        # Process products in batches
        for i in range(0, len(products_data), batch_size):
            batch = products_data[i:i + batch_size]
            current_batch += 1

            # Process each product in the batch within its own transaction
            async with db.begin():
                for edge in batch:
                    product_data = edge["node"]
                    result = await sync_service.db_service.sync_product_to_db(
                        db, product_data
                    )
                    if result["action"] == "created":
                        stats["added"] += 1
                    elif result["action"] == "updated":
                        stats["updated"] += 1
                    else:
                        stats["unchanged"] += 1

                # Update progress after each batch
                processed_items = min((current_batch * batch_size), total_items)
                await progress_service.update_progress(
                    sync_progress_id=sync_progress_id,
                    processed_items=processed_items,
                    current_batch=current_batch
                )

            logger.info(f"Processed and committed batch {current_batch}: {processed_items}/{total_items} products synced")

        # Mark sync as completed
        async with db.begin():
            await progress_service.update_progress(
                sync_progress_id=sync_progress_id,
                processed_items=total_items,
                current_batch=current_batch,
                status="completed"
            )

        return stats

    async def _sync_orders_with_progress(
        self,
        sync_service,
        db,
        progress_service,
        sync_progress_id: int,
        total_items: int
    ) -> Dict[str, int]:
        """Sync orders with progress tracking and batch commits."""

        # Get orders data
        response = await sync_service._paginate_all(sync_service.store_service.get_orders, "orders")
        orders_data = response.get("edges", [])

        stats = {"added": 0, "updated": 0, "unchanged": 0}
        batch_size = 50
        current_batch = 0

        # Process orders in batches
        for i in range(0, len(orders_data), batch_size):
            batch = orders_data[i:i + batch_size]
            current_batch += 1

            # Process each order in the batch within its own transaction
            async with db.begin():
                for edge in batch:
                    order_data = edge["node"]
                    result = await sync_service.db_service.sync_order_to_db(
                        db, order_data
                    )
                    if result["action"] == "created":
                        stats["added"] += 1
                    elif result["action"] == "exists":
                        stats["unchanged"] += 1

                # Update progress after each batch
                processed_items = min((current_batch * batch_size), total_items)
                await progress_service.update_progress(
                    sync_progress_id=sync_progress_id,
                    processed_items=processed_items,
                    current_batch=current_batch
                )

            logger.info(f"Processed and committed batch {current_batch}: {processed_items}/{total_items} orders synced")

        # Mark sync as completed
        async with db.begin():
            await progress_service.update_progress(
                sync_progress_id=sync_progress_id,
                processed_items=total_items,
                current_batch=current_batch,
                status="completed"
            )

        return stats