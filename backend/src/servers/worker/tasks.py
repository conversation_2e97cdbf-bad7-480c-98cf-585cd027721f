"""
Celery tasks for ProductVideo platform.
Defines all background tasks that can be executed asynchronously.
"""

import logging
from .main import celery_app

from servers.worker.processors import (
    MediaGenerationProcessor,
    MediaPushProcessor,
    AnalyticsProcessor,
    SyncProcessor
)

logger = logging.getLogger(__name__)

# Initialize processors
media_processor = MediaGenerationProcessor()
push_processor = MediaPushProcessor()
analytics_processor = AnalyticsProcessor()
sync_processor = SyncProcessor()


@celery_app.task(name='media_generation.generate_media')
def generate_media(job_data):
    """Generate media (video, image, voice) for products."""
    logger.info(f"Starting media generation task with data: {job_data}")
    try:
        result = media_processor.process(job_data)
        logger.info("Media generation task completed successfully")
        return result
    except Exception as e:
        logger.error(f"Media generation task failed: {e}")
        raise


@celery_app.task(name='media_push.push_to_shopify')
def push_to_shopify(job_data):
    """Push generated media to Shopify store."""
    logger.info(f"Starting Shopify push task with data: {job_data}")
    try:
        result = push_processor.process(job_data)
        logger.info("Shopify push task completed successfully")
        return result
    except Exception as e:
        logger.error(f"Shopify push task failed: {e}")
        raise


@celery_app.task(name='analytics.process_analytics')
def process_analytics(job_data):
    """Process analytics events."""
    logger.info(f"Starting analytics processing task with data: {job_data}")
    try:
        result = analytics_processor.process(job_data)
        logger.info("Analytics processing task completed successfully")
        return result
    except Exception as e:
        logger.error(f"Analytics processing task failed: {e}")
        raise


@celery_app.task(name='sync.sync_data')
def sync_data(job_data):
    """Synchronize data with Shopify."""
    import asyncio
    logger.info(f"Starting sync task with data: {job_data}")
    try:
        # Use asyncio.run to create a fresh event loop for this task
        result = asyncio.run(sync_processor.process_async(job_data))
        logger.info("Sync task completed successfully")
        return result
    except Exception as e:
        logger.error(f"Sync task failed: {e}")
        raise