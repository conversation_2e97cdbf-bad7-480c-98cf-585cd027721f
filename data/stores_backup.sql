--
-- PostgreSQL database dump
--

-- Dumped from database version 15.13 (Debian 15.13-1.pgdg120+1)
-- Dumped by pg_dump version 15.13 (Debian 15.13-1.pgdg120+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: stores; Type: TABLE; Schema: public; Owner: app_user
--

CREATE TABLE public.stores (
    id integer NOT NULL,
    name character varying NOT NULL,
    platform character varying NOT NULL,
    api_key character varying,
    api_secret_key character varying,
    admin_access_token character varying,
    storefront_access_token character varying,
    shop_domain character varying,
    shop_id character varying,
    shop_name character varying,
    is_active boolean,
    last_sync timestamp with time zone,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone,
    owner_id integer
);


ALTER TABLE public.stores OWNER TO app_user;

--
-- Name: stores_id_seq; Type: SEQUENCE; Schema: public; Owner: app_user
--

CREATE SEQUENCE public.stores_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.stores_id_seq OWNER TO app_user;

--
-- Name: stores_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: app_user
--

ALTER SEQUENCE public.stores_id_seq OWNED BY public.stores.id;


--
-- Name: stores id; Type: DEFAULT; Schema: public; Owner: app_user
--

ALTER TABLE ONLY public.stores ALTER COLUMN id SET DEFAULT nextval('public.stores_id_seq'::regclass);


--
-- Data for Name: stores; Type: TABLE DATA; Schema: public; Owner: app_user
--

COPY public.stores (id, name, platform, api_key, api_secret_key, admin_access_token, storefront_access_token, shop_domain, shop_id, shop_name, is_active, last_sync, created_at, updated_at, owner_id) FROM stdin;
1	leanchain	shopify	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	a6868f33bda805f18c7eb28f28e636fe	\N	\N	leanchain.myshopify.com	\N	\N	t	2025-07-30 09:34:36.891635+00	2025-06-23 15:42:29+00	2025-07-30 09:34:36+00	1
2	dancing-queens-staging	shopify	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	\N	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	\N	dancing-queens-staging.myshopify.com	\N	\N	t	2025-07-30 12:25:57.850516+00	2025-07-30 11:01:14+00	2025-07-30 12:25:57+00	3
\.


--
-- Name: stores_id_seq; Type: SEQUENCE SET; Schema: public; Owner: app_user
--

SELECT pg_catalog.setval('public.stores_id_seq', 1, false);


--
-- Name: stores stores_pkey; Type: CONSTRAINT; Schema: public; Owner: app_user
--

ALTER TABLE ONLY public.stores
    ADD CONSTRAINT stores_pkey PRIMARY KEY (id);


--
-- Name: ix_stores_id; Type: INDEX; Schema: public; Owner: app_user
--

CREATE INDEX ix_stores_id ON public.stores USING btree (id);


--
-- Name: stores stores_owner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: app_user
--

ALTER TABLE ONLY public.stores
    ADD CONSTRAINT stores_owner_id_fkey FOREIGN KEY (owner_id) REFERENCES public.users(id);


--
-- PostgreSQL database dump complete
--

