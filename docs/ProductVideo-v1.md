# ProductVideo Platform v1.0 - Complete Documentation

## 🎯 **Overview**

ProductVideo is a SaaS platform that automatically generates professional product videos for e-commerce stores using AI. The platform integrates with Shopify to sync products, generate 4 video variants per product, and push videos back to product media.

## 🏗 **Architecture**

### **Backend (FastAPI + PostgreSQL)**
```
backend/src/
├── core/                           # Infrastructure
│   ├── config.py                  # Environment settings
│   ├── db/                        # Async database
│   ├── middleware/                # Request context & logging
│   ├── services/                  # Base CRUD service
│   └── utils/                     # Logging & utilities
└── modules/                       # Feature modules
    ├── auth/                      # JWT authentication
    ├── users/                     # User management
    ├── stores/                    # Store management
    ├── shopify/                   # Shopify integration
    ├── video_generation/          # AI video generation
    ├── analytics/                 # Performance tracking
    ├── webhooks/                  # Webhook handling
    └── billing/                   # Usage tracking
```

### **Frontend (Next.js + shadcn/ui)**
```
frontend/src/
├── components/                    # UI components
│   ├── ui/                       # shadcn/ui components
│   ├── video-generation/         # Video generation UI
│   └── layout/                   # Layout components
├── services/                     # API clients
│   ├── videoService.ts           # Video generation API
│   └── shopifyService.ts         # Shopify integration API
└── app/                          # Next.js app router
```

## 🚀 **Core Features**

### **1. Video Generation Pipeline**
- **AI Providers**: Google Veo 3 (primary), Banana (images), Mock (testing)
- **4 Video Variants**: Square (1:1), Vertical (9:16), Horizontal (16:9), Story (9:16)
- **Templates**: 6+ professional templates with customization
- **Background Processing**: Redis-based job queue with workers
- **Storage**: S3/CloudFlare R2 with CDN integration

### **2. Shopify Integration**
- **OAuth Flow**: Complete Shopify app installation
- **Product Sync**: Real-time product synchronization
- **Media Upload**: GraphQL-based video upload to product media
- **Webhooks**: Product/order event processing
- **Rate Limiting**: Shopify API compliance

### **3. Analytics & Performance**
- **Video Metrics**: Views, plays, completion rates
- **Conversion Tracking**: Revenue attribution
- **A/B Testing**: Variant performance comparison
- **Dashboard**: Real-time performance insights

## 🔧 **Setup & Installation**

### **Prerequisites**
- Docker & Docker Compose
- Node.js 18+ (for frontend development)
- Python 3.11+ (for backend development)

### **Environment Variables**
Create `.env` file in project root:
```bash
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/productvideo
REDIS_URL=redis://localhost:6379

# AI Services
VEO3_API_KEY=your_veo3_api_key
BANANA_API_KEY=your_banana_api_key

# Shopify (per-store, managed in database)
SHOPIFY_API_KEY=your_shopify_api_key
SHOPIFY_API_SECRET=your_shopify_api_secret

# Storage
STORAGE_PROVIDER=s3
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
S3_BUCKET_NAME=your_bucket_name

# CloudFlare R2 (alternative)
CLOUDFLARE_R2_ENDPOINT=your_r2_endpoint
CLOUDFLARE_R2_ACCESS_KEY=your_r2_access_key
CLOUDFLARE_R2_SECRET_KEY=your_r2_secret_key

# Security
JWT_SECRET_KEY=your_jwt_secret_key
ENCRYPTION_KEY=your_encryption_key

# Application
BASE_URL=https://your-domain.com
ENVIRONMENT=production
```

### **Quick Start**
```bash
# Clone and setup
git clone <repository>
cd e-commerce

# Start services
docker-compose up -d

# Run migrations
docker-compose exec backend alembic upgrade head

# Access application
# Frontend: http://localhost:3000
# Backend API: http://localhost:8000
# API Docs: http://localhost:8000/docs
```

## 📡 **API Endpoints**

### **Video Generation**
```
POST   /api/video/generate          # Generate videos for products
GET    /api/video/jobs/{job_id}     # Get job status and variants
POST   /api/video/regenerate        # Regenerate specific variant
POST   /api/video/push              # Push variant to Shopify
GET    /api/video/templates         # Get available templates
GET    /api/video/voices            # Get available voices
```

### **Shopify Integration**
```
GET    /api/shopify/install         # Get OAuth installation URL
GET    /api/shopify/oauth/callback  # Handle OAuth callback
POST   /api/shopify/webhook/verify  # Verify webhook signature
```

### **Analytics**
```
GET    /api/analytics/product/{id}  # Get product video metrics
POST   /api/analytics/events        # Track analytics events
GET    /api/analytics/dashboard     # Get dashboard metrics
```

### **Webhooks**
```
POST   /api/webhooks/shopify/products_create
POST   /api/webhooks/shopify/products_update
POST   /api/webhooks/shopify/orders_create
POST   /api/webhooks/shopify/app_uninstalled
```

## 🔄 **Workflows**

### **Video Generation Flow**
1. **Product Selection**: User selects products from Shopify store
2. **Template & Voice**: Choose template and voice options
3. **Job Creation**: System creates video generation job
4. **Queue Processing**: Job queued for background processing
5. **AI Generation**: Veo3 generates 4 video variants
6. **Storage**: Videos stored in S3/CloudFlare R2
7. **Database Update**: Job status and variants saved
8. **User Notification**: Real-time progress updates

### **Shopify Publishing Flow**
1. **Variant Selection**: User selects best performing variant
2. **Media Upload**: GraphQL mutation to create product media
3. **Position Management**: Set video position in product gallery
4. **SEO Optimization**: Add alt text and descriptions
5. **Analytics Tracking**: Track video performance

## 🏢 **Multi-Tenant Architecture**

### **Store Management**
- Each user can connect multiple Shopify stores
- Store credentials encrypted and stored securely
- Per-store webhook subscriptions
- Isolated video generation and analytics

### **Data Isolation**
- Tenant-based storage paths: `tenant_{id}/year/month/file`
- Database-level tenant filtering
- Separate Redis job queues per tenant
- Isolated analytics and billing

## 🔐 **Security**

### **Authentication**
- JWT-based authentication
- Refresh token rotation
- Role-based access control
- Session management

### **Data Protection**
- Encrypted Shopify credentials
- Secure webhook verification
- HTTPS-only communication
- Input validation and sanitization

## 📊 **Monitoring & Observability**

### **Logging**
- Structured JSON logging
- Request/response tracking
- Error aggregation
- Performance metrics

### **Health Checks**
- Database connectivity
- Redis availability
- External API status
- Worker health monitoring

## 🚀 **Deployment**

### **Production Deployment**
```bash
# Build and deploy
docker-compose -f docker-compose.yml up -d

# Scale workers
docker-compose up -d --scale video-worker=5

# Monitor logs
docker-compose logs -f backend video-worker
```

### **Environment-Specific Configs**
- **Development**: Local database, mock AI providers
- **Staging**: Shared database, real AI providers, test data
- **Production**: Managed database, full AI providers, monitoring

## 📈 **Scaling Considerations**

### **Horizontal Scaling**
- **Backend**: Stateless FastAPI instances behind load balancer
- **Workers**: Auto-scaling based on queue depth
- **Database**: Read replicas for analytics queries
- **Storage**: CDN for video delivery

### **Performance Optimization**
- **Caching**: Redis for frequently accessed data
- **Database**: Proper indexing and query optimization
- **Video Processing**: Parallel variant generation
- **API**: Response compression and pagination

## 🔧 **Development**

### **Local Development**
```bash
# Backend
cd backend
pip install -e .
python src/main.py

# Frontend
cd frontend
npm install
npm run dev

# Workers
python src/modules/video_generation/worker_manager.py
```

### **Testing**
```bash
# Backend tests
cd backend
pytest

# Frontend tests
cd frontend
npm test

# Integration tests
./scripts/test_integration.sh
```

## 📋 **Roadmap**

### **v1.1 - Enhanced Features**
- Advanced video editing capabilities
- Multi-platform publishing (Instagram, TikTok)
- Advanced analytics and A/B testing
- Team collaboration features

### **v1.2 - Enterprise Features**
- White-label solutions
- Advanced workflow automation
- Enterprise SSO integration
- Advanced reporting and insights

---

**ProductVideo v1.0** - Ready for production deployment and scaling to thousands of users! 🚀
