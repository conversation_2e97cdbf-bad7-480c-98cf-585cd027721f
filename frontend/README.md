# ProductVideo Frontend

A modern React/Next.js frontend for the ProductVideo platform - AI-powered video generation for Shopify merchants.

## 🚀 Features

- **Shopify Integration**: OAuth connection, product browsing, video pushing
- **Video Generation**: AI-powered video creation with templates and voices
- **Video Management**: Preview, regenerate, favorite, and organize videos
- **Analytics Dashboard**: Performance tracking, conversion metrics, A/B testing
- **Gallery**: Search, filter, and bulk manage generated videos
- **Responsive Design**: Works seamlessly on desktop, tablet, and mobile
- **Accessibility**: WCAG compliant with keyboard navigation and screen reader support

## 🛠️ Tech Stack

- **Framework**: Next.js 14 with App Router
- **UI Library**: shadcn/ui components with Tailwind CSS
- **State Management**: React Query (TanStack Query) for server state
- **Charts**: Recharts for analytics visualization
- **Icons**: Lucide React
- **Notifications**: React Hot Toast
- **Forms**: React Hook Form with Zod validation
- **Testing**: Playwright for E2E, Jest for unit tests
- **Development**: Storybook for component development

## 📋 Prerequisites

- Node.js 18+ and npm/yarn/pnpm
- Backend API running (see backend README)

## 🚀 Quick Start

### 1. Install Dependencies

```bash
npm install
# or
yarn install
# or
pnpm install
```

### 2. Environment Configuration

Create `.env.local` file:

```env
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000

# Shopify Configuration (for OAuth redirects)
NEXT_PUBLIC_SHOPIFY_CLIENT_ID=your_shopify_client_id
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Analytics (optional)
NEXT_PUBLIC_GA_ID=your_google_analytics_id
```

### 3. Development Server

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📁 Project Structure

```
frontend/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── (app)/             # Authenticated app routes
│   │   │   ├── video-generation/
│   │   │   ├── connect-shopify/
│   │   │   └── analytics/
│   │   ├── (auth)/            # Authentication pages
│   │   └── layout.tsx         # Root layout
│   ├── components/            # Reusable components
│   │   ├── ui/               # shadcn/ui components
│   │   ├── video-generation/ # Video-specific components
│   │   ├── analytics/        # Analytics components
│   │   └── layout/           # Layout components
│   ├── services/             # API services
│   │   ├── api.ts           # Base API client
│   │   ├── videoService.ts  # Video generation API
│   │   ├── shopifyService.ts # Shopify integration API
│   │   └── analyticsService.ts # Analytics API
│   ├── hooks/               # Custom React hooks
│   ├── lib/                 # Utilities and configurations
│   ├── types/               # TypeScript type definitions
│   └── styles/              # Global styles
├── public/                  # Static assets
├── tests/                   # Test files
│   ├── e2e/                # Playwright E2E tests
│   └── unit/               # Jest unit tests
├── stories/                # Storybook stories
└── docs/                   # Documentation
```

## 🧩 Key Components

### Video Generation Flow

1. **ProductSelectGrid**: Browse and select Shopify products
2. **GenerateModal**: Configure video generation (template, voice, etc.)
3. **VariantCards**: Preview and manage generated video variants
4. **PushModal**: Push selected videos to Shopify

### Analytics & Management

1. **AnalyticsDashboard**: Performance metrics and charts
2. **Gallery**: Search, filter, and bulk manage videos
3. **ConnectShopify**: OAuth connection and store management

## 🎨 Component Development

### Using Storybook

```bash
npm run storybook
```

View components in isolation at [http://localhost:6006](http://localhost:6006).

### Adding New Components

1. Create component in appropriate directory
2. Add TypeScript interfaces
3. Include accessibility attributes
4. Write Storybook story
5. Add unit tests

Example component structure:

```tsx
"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface MyComponentProps {
  title: string;
  onAction: () => void;
}

export function MyComponent({ title, onAction }: MyComponentProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <Button onClick={onAction}>Action</Button>
      </CardContent>
    </Card>
  );
}
```

## 🧪 Testing

### Unit Tests

```bash
npm run test
# or
npm run test:watch
```

### E2E Tests

```bash
npm run test:e2e
# or
npm run test:e2e:ui  # with Playwright UI
```

### Test Coverage

```bash
npm run test:coverage
```

## 📊 Analytics Integration

The frontend automatically tracks video events:

- **Video Views**: When video appears in viewport
- **Video Plays**: When user clicks play
- **Progress Events**: 25%, 50%, 75%, 100% completion
- **CTA Clicks**: Call-to-action button interactions
- **Conversions**: Add to cart and purchase events

Events are sent to the backend analytics service with deduplication.

## 🎯 Accessibility

- **Keyboard Navigation**: All interactive elements accessible via keyboard
- **Screen Readers**: Proper ARIA labels and semantic HTML
- **Color Contrast**: WCAG AA compliant color schemes
- **Focus Management**: Clear focus indicators and logical tab order
- **Video Controls**: Accessible video player controls

## 🚀 Deployment

### Production Build

```bash
npm run build
npm run start
```

### Docker Deployment

```bash
docker build -t productvideo-frontend .
docker run -p 3000:3000 productvideo-frontend
```

### Environment Variables for Production

```env
NEXT_PUBLIC_API_URL=https://api.productvideo.com
NEXT_PUBLIC_WS_URL=wss://api.productvideo.com
NEXT_PUBLIC_SHOPIFY_CLIENT_ID=your_production_shopify_client_id
NEXT_PUBLIC_APP_URL=https://app.productvideo.com
```

## 🔧 Configuration

### API Client Configuration

The API client automatically handles:

- Authentication tokens
- Request/response interceptors
- Error handling and retries
- Loading states

### Theme Customization

Modify `src/styles/globals.css` for theme customization:

```css
:root {
  --primary: 262.1 83.3% 57.8%;
  --secondary: 220 14.3% 95.9%;
  /* ... other CSS variables */
}
```

## 📈 Performance

- **Code Splitting**: Automatic route-based code splitting
- **Image Optimization**: Next.js Image component with lazy loading
- **Bundle Analysis**: `npm run analyze` to analyze bundle size
- **Caching**: React Query for intelligent data caching
- **Prefetching**: Automatic prefetching of linked pages

## 🐛 Troubleshooting

### Common Issues

1. **API Connection Failed**
   ```bash
   # Check if backend is running
   curl http://localhost:8000/health
   
   # Verify environment variables
   echo $NEXT_PUBLIC_API_URL
   ```

2. **Shopify OAuth Issues**
   - Verify `NEXT_PUBLIC_SHOPIFY_CLIENT_ID` is correct
   - Check redirect URLs in Shopify app settings
   - Ensure HTTPS in production

3. **Video Playback Issues**
   - Check CORS settings on video URLs
   - Verify video format compatibility
   - Test with different browsers

4. **Build Errors**
   ```bash
   # Clear Next.js cache
   rm -rf .next
   
   # Reinstall dependencies
   rm -rf node_modules package-lock.json
   npm install
   ```

### Debug Mode

```bash
# Enable debug logging
DEBUG=* npm run dev

# Or specific modules
DEBUG=api:* npm run dev
```

## 🤝 Contributing

1. Fork the repository
2. Create feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open Pull Request

### Code Style

- Use TypeScript for all new code
- Follow ESLint and Prettier configurations
- Write tests for new components
- Update Storybook stories
- Add JSDoc comments for complex functions

## 📞 Support

- **Documentation**: [Link to docs]
- **Issues**: [GitHub Issues]
- **Discord**: [Community Discord]
- **Email**: <EMAIL>

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.
