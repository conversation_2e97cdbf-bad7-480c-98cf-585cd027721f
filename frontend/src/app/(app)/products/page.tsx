'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Package, Plus, Search, Loader2 } from 'lucide-react';
import { productService, Product } from '@/services/productService';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';

const Products: React.FC = () => {
  const router = useRouter();
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const itemsPerPage = 50;

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        const data = await productService.getProducts(currentPage, itemsPerPage);
        setProducts(data);
        // For now, we'll estimate total pages based on current data length
        // In a real implementation, the backend should return total count
        setTotalPages(Math.ceil(data.length / itemsPerPage) || 1);
        setError(null);
      } catch (err) {
        console.error('Failed to fetch products:', err);
        setError('Failed to load products. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [currentPage]);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Products</h1>
            <p className="text-muted-foreground">Manage your product catalog</p>
          </div>
        </div>
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading products...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Products</h1>
            <p className="text-muted-foreground">Manage your product catalog</p>
          </div>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Products</h1>
          <p className="text-muted-foreground">Manage your product catalog</p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Add Product
        </Button>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center space-x-2">
            <Search className="h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search products..."
              className="flex-1 border-0 bg-transparent text-sm placeholder:text-muted-foreground focus:outline-none"
            />
          </div>
        </CardContent>
      </Card>

      {/* Products Vertical Layout */}
      <div className="space-y-4">
        {products.length === 0 ? (
          <div className="text-center py-12">
            <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No products found</h3>
            <p className="text-muted-foreground mb-4">Products will appear here when available from your stores</p>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Product
            </Button>
          </div>
        ) : (
          products.map((product) => (
            <Card key={product.id} className="w-full">
              <CardContent className="p-6">
                {/* Images Row */}
                <div className="flex gap-2 mb-4 overflow-x-auto">
                  {(() => {
                    // Try to get images from product.images
                    let imageArray: any[] = [];

                    if (product?.images) {
                      try {
                        const parsed = JSON.parse(product.images);
                        // Check if it's an array of strings (URLs) or objects
                        if (Array.isArray(parsed)) {
                          if (parsed.length > 0 && typeof parsed[0] === 'string') {
                            // Array of URL strings - convert to objects
                            imageArray = parsed.map(url => ({ url }));
                          } else {
                            // Array of objects
                            imageArray = parsed;
                          }
                        } else if (typeof parsed === 'string') {
                          // Single URL string
                          imageArray = [{ url: parsed }];
                        } else {
                          // Single object
                          imageArray = [parsed];
                        }
                      } catch (e) {
                        console.warn('Failed to parse product images:', e);
                      }
                    }

                    // If no images from product.images, try featured_image
                    if (imageArray.length === 0 && product?.featured_image) {
                      try {
                        const featured = JSON.parse(product.featured_image);
                        if (featured && featured.url) {
                          imageArray = [featured];
                        }
                      } catch (e) {
                        console.warn('Failed to parse featured image:', e);
                      }
                    }

                    // Display images or fallback
                    return imageArray.length > 0 ? (
                      imageArray.map((image, index) => {
                        const imageUrl = image?.url || image?.original_src || image?.transformed_src || image?.src;
                        return imageUrl ? (
                          <img
                            key={index}
                            src={imageUrl}
                            alt={`${product?.title || 'Product'} - Image ${index + 1}`}
                            className="h-20 w-20 object-cover rounded flex-shrink-0"
                            onError={(e) => {
                              console.warn('Image failed to load:', imageUrl);
                              e.currentTarget.style.display = 'none';
                            }}
                          />
                        ) : null;
                      }).filter(Boolean)
                    ) : (
                      <Package className="h-20 w-20 text-muted-foreground flex-shrink-0" />
                    );
                  })()}
                </div>

                {/* Title and Info Row */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold">{product.title}</h3>
                    <div className="flex flex-wrap gap-2 text-sm text-muted-foreground mb-2">
                      {product.sku && <span>SKU: {product.sku}</span>}
                      {product.product_type && <span>Type: {product.product_type}</span>}
                      {product.vendor && <span>Vendor: {product.vendor}</span>}
                    </div>
                    <div className="flex flex-wrap gap-2 text-sm text-muted-foreground mb-2">
                      {product.handle && <span>Handle: {product.handle}</span>}
                      <span>Status: {product.status}</span>
                      {product.total_inventory !== undefined && <span>Inventory: {product.total_inventory}</span>}
                    </div>
                    {product.tags && (
                      <div className="flex flex-wrap gap-1 mb-2">
                        {product.tags.split(', ').map((tag, index) => (
                          <span key={index} className="px-2 py-1 bg-gray-100 text-xs rounded">
                            {tag}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                  <div className="text-right ml-4">
                    <p className="text-2xl font-bold">${product.price.toFixed(2)}</p>
                    {product.compare_at_price && product.compare_at_price > product.price && (
                      <p className="text-sm text-muted-foreground line-through">
                        ${product.compare_at_price.toFixed(2)}
                      </p>
                    )}
                    <p className="text-sm text-muted-foreground">{product.quantity} in stock</p>
                    {product.total_variants > 1 && (
                      <p className="text-sm text-muted-foreground">{product.total_variants} variants</p>
                    )}
                  </div>
                </div>

                {/* Description Row - Full Width */}
                {product.description && (
                  <div className="border-t pt-4">
                    <div
                      className="text-sm text-muted-foreground"
                      dangerouslySetInnerHTML={{
                        __html: product.description.includes('<') && product.description.includes('>')
                          ? product.description
                          : product.description.replace(/\n/g, '<br>')
                      }}
                    />
                  </div>
                )}

                {/* Actions Row */}
                <div className="mt-4 flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => router.push(`/products/${product.id}`)}
                  >
                    View Details
                  </Button>
                  {product.online_store_url && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(product.online_store_url, '_blank')}
                    >
                      View on Store
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Pagination Controls */}
      {products.length > 0 && (
        <div className="mt-6">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() => totalPages > 1 && setCurrentPage(prev => Math.max(1, prev - 1))}
                  className={currentPage === 1 || totalPages === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                />
              </PaginationItem>

              {/* Page Numbers */}
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNum;
                if (totalPages <= 5) {
                  pageNum = i + 1;
                } else if (currentPage <= 3) {
                  pageNum = i + 1;
                } else if (currentPage >= totalPages - 2) {
                  pageNum = totalPages - 4 + i;
                } else {
                  pageNum = currentPage - 2 + i;
                }

                return (
                  <PaginationItem key={pageNum}>
                    <PaginationLink
                      onClick={() => totalPages > 1 && setCurrentPage(pageNum)}
                      isActive={currentPage === pageNum}
                      className={totalPages > 1 ? "cursor-pointer" : ""}
                    >
                      {pageNum}
                    </PaginationLink>
                  </PaginationItem>
                );
              })}

              {/* Ellipsis for large page counts */}
              {totalPages > 5 && currentPage < totalPages - 2 && (
                <PaginationItem>
                  <PaginationEllipsis />
                </PaginationItem>
              )}

              <PaginationItem>
                <PaginationNext
                  onClick={() => totalPages > 1 && setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  className={currentPage === totalPages || totalPages === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>

          <div className="text-center text-sm text-muted-foreground mt-2">
            Page {currentPage} of {totalPages} ({products.length} products shown)
          </div>
        </div>
      )}
    </div>
  );
};

export default Products;
