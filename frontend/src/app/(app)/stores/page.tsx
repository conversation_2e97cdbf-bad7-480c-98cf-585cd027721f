"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Store, Plus, Settings, BarChart3, Loader2 } from "lucide-react";
import { storeService, Store as StoreType } from "@/services/storeService";

const Stores: React.FC = () => {
  const router = useRouter();
  const [stores, setStores] = useState<StoreType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStores = async () => {
      try {
        setLoading(true);
        const data = await storeService.getStores();
        setStores(data);
        setError(null);
      } catch (err) {
        console.error("Failed to fetch stores:", err);
        setError("Failed to load stores. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchStores();
  }, []);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Stores</h1>
            <p className="text-muted-foreground">
              Manage your connected store platforms
            </p>
          </div>
        </div>
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading stores...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Stores</h1>
            <p className="text-muted-foreground">
              Manage your connected store platforms
            </p>
          </div>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>Try Again</Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Stores</h1>
          <p className="text-muted-foreground">
            Manage your connected store platforms
          </p>
        </div>
        <Button onClick={() => router.push("/stores/connect")}>
          <Plus className="mr-2 h-4 w-4" />
          Connect Store
        </Button>
      </div>

      {/* Stores Grid */}
      <div className="grid gap-4 md:grid-cols-4">
        {stores.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <Store className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No stores connected</h3>
            <p className="text-muted-foreground mb-4">
              Connect your first store to get started
            </p>
            <Button onClick={() => router.push("/stores/connect")}>
              <Plus className="mr-2 h-4 w-4" />
              Connect Store
            </Button>
          </div>
        ) : (
          stores.map((store) => (
            <Card key={store.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Store className="h-8 w-8 text-muted-foreground" />
                    <div>
                      <CardTitle>{store.name}</CardTitle>
                      <CardDescription>{store.platform}</CardDescription>
                    </div>
                  </div>
                  <span
                    className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
                      store.is_active
                        ? "bg-green-100 text-green-800"
                        : "bg-red-100 text-red-800"
                    }`}
                  >
                    {store.is_active ? "Active" : "Inactive"}
                  </span>
                </div>
              </CardHeader>
              <CardContent>
                <div className="mb-4">
                  {store.shop_name && (
                    <p className="text-sm text-muted-foreground">
                      Shop: {store.shop_name}
                    </p>
                  )}
                  {store.last_sync && (
                    <p className="text-sm text-muted-foreground">
                      Last sync:{" "}
                      {new Date(store.last_sync).toLocaleDateString()}
                    </p>
                  )}
                </div>

                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1"
                    onClick={() =>
                      router.push(`/stores/settings?storeId=${store.id}`)
                    }
                  >
                    <Settings className="mr-2 h-4 w-4" />
                    Settings
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1"
                    onClick={() => router.push("/dashboard")}
                  >
                    <BarChart3 className="mr-2 h-4 w-4" />
                    Analytics
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};

export default Stores;
