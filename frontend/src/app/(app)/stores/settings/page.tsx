'use client';

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Settings, FolderSyncIcon, Clock, CheckCircle, Loader2, Save, RotateCcw } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '@/services/api';

interface SyncSettingsData {
  productSync: string;
  inventorySync: string;
  customerSync: string;
}

interface SyncStatus {
  products: { status: 'success' | 'syncing' | 'error'; lastSync?: string };
}

interface SyncProgress {
  id: number;
  store_id: number;
  sync_type: string;
  status: 'running' | 'completed' | 'failed' | 'paused';
  total_items: number;
  processed_items: number;
  current_batch: number;
  total_batches: number;
  progress_percentage: number;
  last_update: string;
  created_at: string;
  completed_at?: string;
  error_message?: string;
}

interface StoreDetails {
  id: number;
  name: string;
  shop_domain?: string;
  shop_name?: string;
}

const defaultSettings: SyncSettingsData = {
  productSync: 'Every 30 minutes',
  inventorySync: 'Every 5 minutes',
  customerSync: 'Every hour',
};

const SyncSettings: React.FC = () => {
  const searchParams = useSearchParams();
  const storeId = searchParams.get('storeId');

  const [settings, setSettings] = useState<SyncSettingsData>(defaultSettings);
  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    products: { status: 'success', lastSync: '5 minutes ago' },
  });
  const [syncProgress, setSyncProgress] = useState<SyncProgress | null>(null);
  const [progressHistory, setProgressHistory] = useState<string[]>([]);

  // Fetch settings using React Query
  const {
    data: settingsData,
    isLoading: isLoadingSettings,
    error: settingsError
  } = useQuery({
    queryKey: ['store-settings', storeId],
    queryFn: async () => {
      if (!storeId) throw new Error('Store ID is required');

      const response = await api.get(`/api/stores/${storeId}/sync-settings`);
      return response.data;
    },
    enabled: !!storeId,
    retry: 2,
    retryDelay: 1000,
  });

  // Fetch store details using React Query
  const {
    data: storeDetails,
    isLoading: isLoadingStoreDetails,
    error: storeDetailsError
  } = useQuery<StoreDetails>({
    queryKey: ['store-details', storeId],
    queryFn: async () => {
      if (!storeId) throw new Error('Store ID is required');
      const response = await api.get(`/api/stores/${storeId}`);
      return response.data;
    },
    enabled: !!storeId,
    retry: 2,
    retryDelay: 1000,
  });

  // Fetch sync progress using React Query with polling
  const {
    data: syncProgressData,
    refetch: refetchProgress
  } = useQuery<SyncProgress>({
    queryKey: ['sync-progress', storeId, 'products'],
    queryFn: async () => {
      if (!storeId) throw new Error('Store ID is required');
      const response = await api.get(`/api/stores/${storeId}/sync-progress/products`);
      return response.data;
    },
    enabled: !!storeId,
    refetchInterval: (data) => {
      // Poll every 2 seconds if sync is running, otherwise stop polling
      return data?.status === 'running' ? 2000 : false;
    },
    retry: 1,
    retryDelay: 1000,
  });

  // Update local state when data is loaded
  useEffect(() => {
    if (settingsData) {
      setSettings({
        productSync: settingsData.product_sync_interval,
        inventorySync: settingsData.inventory_sync_interval,
        customerSync: settingsData.customer_sync_interval,
      });
    }
  }, [settingsData]);

  // Update sync progress and history
  useEffect(() => {
    if (syncProgressData) {
      setSyncProgress(syncProgressData);

      // Update sync status based on progress
      if (syncProgressData.status === 'running') {
        setSyncStatus(prev => ({
          ...prev,
          products: { status: 'syncing', lastSync: `Processing ${syncProgressData.processed_items}/${syncProgressData.total_items} items` }
        }));

        // Add to progress history for rolling updates
        const progressMessage = `${syncProgressData.processed_items} products synced`;
        setProgressHistory(prev => {
          const newHistory = [progressMessage, ...prev.slice(0, 4)]; // Keep last 5 updates
          return newHistory;
        });
      } else if (syncProgressData.status === 'completed') {
        setSyncStatus(prev => ({
          ...prev,
          products: { status: 'success', lastSync: `Completed at ${new Date(syncProgressData.completed_at || '').toLocaleTimeString()}` }
        }));
      } else if (syncProgressData.status === 'failed') {
        setSyncStatus(prev => ({
          ...prev,
          products: { status: 'error', lastSync: `Failed: ${syncProgressData.error_message || 'Unknown error'}` }
        }));
      }
    }
  }, [syncProgressData]);

  const queryClient = useQueryClient();

  const saveSettingsMutation = useMutation({
    mutationFn: async () => {
      if (!storeId) throw new Error('Store ID is required');

      const response = await api.put(`/api/stores/${storeId}/sync-settings`, {
        product_sync_interval: settings.productSync,
        inventory_sync_interval: settings.inventorySync,
        customer_sync_interval: settings.customerSync,
      });

      return response.data;
    },
    onSuccess: () => {
      toast.success('Settings saved successfully');
      queryClient.invalidateQueries({ queryKey: ['store-settings', storeId] });
    },
    onError: (error) => {
      console.error('Failed to save settings:', error);
      toast.error('Failed to save settings');
    },
  });

  const resetToDefault = () => {
    setSettings(defaultSettings);
    toast.success('Settings reset to default');
  };

  const syncMutation = useMutation({
    mutationFn: async (syncType: string) => {
      if (!storeId) throw new Error('Store ID is required');

      const response = await api.post(`/api/stores/${storeId}/sync/products`);
      return response.data;
    },
    onSuccess: (data) => {
      toast.success('Product sync job has been queued');
      // Update sync status to show it's in progress
      setSyncStatus(prev => ({
        ...prev,
        products: { status: 'syncing', lastSync: 'Starting...' }
      }));
      // Clear previous progress history
      setProgressHistory([]);
      // Trigger immediate progress fetch
      refetchProgress();
    },
    onError: (error) => {
      console.error('Failed to start sync:', error);
      toast.error('Failed to start sync job');
    },
  });

  const testConnectionMutation = useMutation({
    mutationFn: async () => {
      if (!storeId) throw new Error('Store ID is required');

      const response = await api.post(`/api/stores/${storeId}/test-connection`);
      return response.data;
    },
    onSuccess: (data) => {
      if (data.success) {
        toast.success(`Connection test successful: ${data.message}`);
      } else {
        toast.error(`Connection test failed: ${data.message}`);
      }
    },
    onError: (error: any) => {
      console.error('Failed to test connection:', error);
      toast.error(`Connection test failed: ${error?.response?.data?.detail || 'Unknown error'}`);
    },
  });

  const updateSetting = (key: keyof SyncSettingsData, value: string) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };
  if (isLoadingSettings || isLoadingStoreDetails) {
    return (
      <div className="space-y-8">
        {/* Loading Header */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="h-8 bg-gray-200 rounded w-48 animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded w-64 mt-2 animate-pulse"></div>
            </div>
            <div className="flex items-center space-x-2">
              <div className="h-3 w-3 bg-gray-300 rounded-full animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div>
            </div>
          </div>
          <div className="h-24 bg-gray-100 rounded-lg animate-pulse"></div>
        </div>

        {/* Loading Content */}
        <div className="grid gap-6 md:grid-cols-2">
          <div className="h-48 bg-gray-100 rounded-lg animate-pulse"></div>
          <div className="h-48 bg-gray-100 rounded-lg animate-pulse"></div>
        </div>

        <div className="h-96 bg-gray-100 rounded-lg animate-pulse"></div>
      </div>
    );
  }

  if (!storeId) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="text-red-600 mb-4">⚠️ Store ID is required</div>
            <p className="text-muted-foreground">Please navigate to this page from the stores list.</p>
          </div>
        </div>
      </div>
    );
  }

  // Show errors if any queries failed
  const hasErrors = settingsError || storeDetailsError;

  return (
    <div className="space-y-8">
      {/* Error Display */}
      {hasErrors && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <div className="text-red-600">⚠️</div>
            <div>
              <h4 className="font-medium text-red-900">Failed to load some data</h4>
              <p className="text-sm text-red-700">
                {settingsError && 'Settings data could not be loaded. '}
                {storeDetailsError && 'Store details could not be loaded. '}
                Please try refreshing the page.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Header Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Store Settings</h1>
            <p className="text-muted-foreground mt-2">
              Configure synchronization and manage your store connection
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <div className={`h-3 w-3 rounded-full animate-pulse ${
              hasErrors ? 'bg-red-500' : 'bg-green-500'
            }`}></div>
            <span className="text-sm text-muted-foreground">
              {hasErrors ? 'Issues Detected' : 'Connected'}
            </span>
          </div>
        </div>

        {/* Store Info Card */}
        {storeDetails && (
          <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Settings className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">{storeDetails.name || storeDetails.shop_name || 'Unnamed Store'}</h3>
                    <p className="text-muted-foreground">{storeDetails.shop_domain || `Store ID: ${storeId}`}</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm text-muted-foreground">Store ID</div>
                  <div className="font-mono text-lg font-semibold">{storeId}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Sync Status Overview */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FolderSyncIcon className="h-5 w-5 text-blue-600" />
              <span>Synchronization Status</span>
            </CardTitle>
            <CardDescription>Real-time sync status across all data types</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className={`flex items-center justify-between p-4 rounded-lg border-2 transition-all ${
                syncStatus.products.status === 'success'
                  ? 'bg-green-50 border-green-200 shadow-sm'
                  : syncStatus.products.status === 'syncing'
                  ? 'bg-blue-50 border-blue-200 shadow-sm'
                  : 'bg-red-50 border-red-200'
              }`}>
                <div className="flex items-center space-x-3">
                  {syncStatus.products.status === 'success' ? (
                    <CheckCircle className="h-8 w-8 text-green-600" />
                  ) : syncStatus.products.status === 'syncing' ? (
                    <Loader2 className="h-8 w-8 text-blue-600 animate-spin" />
                  ) : (
                    <Clock className="h-8 w-8 text-red-600" />
                  )}
                  <div>
                    <p className={`font-semibold text-lg ${
                      syncStatus.products.status === 'success'
                        ? 'text-green-900'
                        : syncStatus.products.status === 'syncing'
                        ? 'text-blue-900'
                        : 'text-red-900'
                    }`}>Products</p>
                    <p className={`text-sm ${
                      syncStatus.products.status === 'success'
                        ? 'text-green-700'
                        : syncStatus.products.status === 'syncing'
                        ? 'text-blue-700'
                        : 'text-red-700'
                    }`}>
                      {syncStatus.products.lastSync || 'Syncing...'}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className={`text-xs font-medium px-2 py-1 rounded-full ${
                    syncStatus.products.status === 'success'
                      ? 'bg-green-100 text-green-800'
                      : syncStatus.products.status === 'syncing'
                      ? 'bg-blue-100 text-blue-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {syncStatus.products.status.toUpperCase()}
                  </div>
                </div>
              </div>

              {/* Progress Bar */}
              {syncProgress && syncProgress.status === 'running' && (
                <div className="bg-white p-4 rounded-lg border border-blue-200">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-blue-900">Sync Progress</span>
                    <span className="text-sm text-blue-700">
                      {syncProgress.processed_items}/{syncProgress.total_items}
                    </span>
                  </div>
                  <div className="w-full bg-blue-100 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${syncProgress.progress_percentage}%` }}
                    ></div>
                  </div>
                  <div className="text-xs text-blue-600 mt-1">
                    Batch {syncProgress.current_batch}/{syncProgress.total_batches}
                  </div>
                </div>
              )}

              {/* Progress History */}
              {progressHistory.length > 0 && (
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Recent Updates</h4>
                  <div className="space-y-1 max-h-32 overflow-y-auto">
                    {progressHistory.map((message, index) => (
                      <div key={index} className="text-xs text-gray-600 flex items-center">
                        <div className="w-1 h-1 bg-blue-500 rounded-full mr-2"></div>
                        {message}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-purple-600" />
              <span>Quick Actions</span>
            </CardTitle>
            <CardDescription>Common management tasks</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <Button
                variant="outline"
                className="w-full justify-start h-12"
                onClick={() => syncMutation.mutate('products')}
                disabled={syncMutation.isPending}
              >
                {syncMutation.isPending ? (
                  <Loader2 className="h-5 w-5 mr-3 animate-spin" />
                ) : (
                  <FolderSyncIcon className="h-5 w-5 mr-3" />
                )}
                <div className="text-left">
                  <div className="font-medium">Sync Products Now</div>
                  <div className="text-xs text-muted-foreground">Manual synchronization</div>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Sync Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Clock className="h-5 w-5 text-orange-600" />
            <span>Synchronization Settings</span>
          </CardTitle>
          <CardDescription>Configure how often different types of data should be synchronized</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid gap-6 md:grid-cols-3">
            {/* Product Sync */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
                <label className="text-sm font-semibold text-gray-900">Product Sync</label>
              </div>
              <Select value={settings.productSync} onValueChange={(value) => updateSetting('productSync', value)}>
                <SelectTrigger className="w-full">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Every 15 minutes">Every 15 minutes</SelectItem>
                  <SelectItem value="Every 30 minutes">Every 30 minutes</SelectItem>
                  <SelectItem value="Every hour">Every hour</SelectItem>
                  <SelectItem value="Every 6 hours">Every 6 hours</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">How often to sync product catalog</p>
            </div>

            {/* Inventory Sync */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                <label className="text-sm font-semibold text-gray-900">Inventory Sync</label>
              </div>
              <Select value={settings.inventorySync} onValueChange={(value) => updateSetting('inventorySync', value)}>
                <SelectTrigger className="w-full">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Real-time">Real-time</SelectItem>
                  <SelectItem value="Every 5 minutes">Every 5 minutes</SelectItem>
                  <SelectItem value="Every 15 minutes">Every 15 minutes</SelectItem>
                  <SelectItem value="Every 30 minutes">Every 30 minutes</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">Stock level synchronization</p>
            </div>

            {/* Customer Sync */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <div className="h-2 w-2 bg-purple-500 rounded-full"></div>
                <label className="text-sm font-semibold text-gray-900">Customer Sync</label>
              </div>
              <Select value={settings.customerSync} onValueChange={(value) => updateSetting('customerSync', value)}>
                <SelectTrigger className="w-full">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Every hour">Every hour</SelectItem>
                  <SelectItem value="Every 6 hours">Every 6 hours</SelectItem>
                  <SelectItem value="Daily">Daily</SelectItem>
                  <SelectItem value="Manual only">Manual only</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">Customer data synchronization</p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row justify-end gap-3 pt-6 border-t">
            <Button
              variant="outline"
              onClick={resetToDefault}
              className="flex items-center space-x-2"
            >
              <RotateCcw className="h-4 w-4" />
              <span>Reset to Default</span>
            </Button>
            <Button
              onClick={() => saveSettingsMutation.mutate()}
              disabled={saveSettingsMutation.isPending}
              className="flex items-center space-x-2"
            >
              {saveSettingsMutation.isPending ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Save className="h-4 w-4" />
              )}
              <span>{saveSettingsMutation.isPending ? 'Saving...' : 'Save Settings'}</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Advanced Actions */}
      <Card className="border-dashed">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5 text-gray-600" />
            <span>Advanced Actions</span>
          </CardTitle>
          <CardDescription>Additional management and diagnostic tools</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="p-4 border rounded-lg hover:bg-gray-50 transition-colors">
              <div className="flex items-center space-x-3 mb-3">
                <div className="h-8 w-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <FolderSyncIcon className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <h4 className="font-medium">Manual Sync</h4>
                  <p className="text-sm text-muted-foreground">Trigger immediate sync</p>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={() => syncMutation.mutate('products')}
                disabled={syncMutation.isPending}
              >
                {syncMutation.isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Syncing...
                  </>
                ) : (
                  'Sync Now'
                )}
              </Button>
            </div>

            <div className="p-4 border rounded-lg hover:bg-gray-50 transition-colors">
              <div className="flex items-center space-x-3 mb-3">
                <div className={`h-8 w-8 rounded-lg flex items-center justify-center ${
                  testConnectionMutation.isPending
                    ? 'bg-yellow-100'
                    : testConnectionMutation.data?.success
                    ? 'bg-green-100'
                    : testConnectionMutation.data?.success === false
                    ? 'bg-red-100'
                    : 'bg-green-100'
                }`}>
                  {testConnectionMutation.isPending ? (
                    <Loader2 className="h-4 w-4 text-yellow-600 animate-spin" />
                  ) : testConnectionMutation.data?.success ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : testConnectionMutation.data?.success === false ? (
                    <Clock className="h-4 w-4 text-red-600" />
                  ) : (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  )}
                </div>
                <div>
                  <h4 className="font-medium">Connection Test</h4>
                  <p className="text-sm text-muted-foreground">
                    {testConnectionMutation.isPending
                      ? 'Testing connection...'
                      : testConnectionMutation.data?.success
                      ? 'Connection successful'
                      : testConnectionMutation.data?.success === false
                      ? 'Connection failed'
                      : 'Verify store connection'
                    }
                  </p>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={() => testConnectionMutation.mutate()}
                disabled={testConnectionMutation.isPending}
              >
                {testConnectionMutation.isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Testing...
                  </>
                ) : (
                  'Test Connection'
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SyncSettings;
