"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useLayout } from "@/contexts/LayoutContext";
import { Button } from "@/components/ui/button";
import {
  Home,
  Package,
  ShoppingCart,
  Users,
  Store,
  Settings,
  BarChart3,
  Webhook,
  ChevronLeft,
  ChevronRight,
  ChevronDown,
  BookOpen,
  HelpCircle,
  MessageCircle,
  Phone,
  Video,
  Link as LinkIcon,
} from "lucide-react";
import { cn } from "@/lib/utils";
import useNavigationTracking from "@/hooks/useNavigationTracking";

const navigation = [
  { name: "Dashboard", href: "/dashboard", icon: Home },
  { name: "Products", href: "/products", icon: Package },
  { name: "Stores", href: "/stores", icon: Store },
];

const commerceNavigation = [
  { name: "Orders", href: "/orders", icon: ShoppingCart },
  { name: "Customers", href: "/customers", icon: Users },
  { name: "Video Generation", href: "/video-generation", icon: Video },
  { name: "Webhooks", href: "/webhook-monitor", icon: Webhook },
];

const helpNavigation = [
  { name: "Documentation", href: "/docs", icon: BookOpen },
  { name: "Help Center", href: "/help", icon: HelpCircle },
  { name: "FAQ", href: "/faq", icon: HelpCircle },
  { name: "Support", href: "/support", icon: MessageCircle },
];

export const Sidebar: React.FC = () => {
  const pathname = usePathname();
  const { sidebarCollapsed, setSidebarCollapsed, isMobile } = useLayout();
  const { trackNavClick } = useNavigationTracking({ section: "Sidebar" });
  const [commerceDropdownOpen, setCommerceDropdownOpen] = React.useState(false);

  if (isMobile) {
    return null; // Handle mobile sidebar separately if needed
  }

  return (
    <div
      className={cn(
        "fixed left-0 top-0 z-40 h-screen bg-card border-r transition-all duration-300",
        sidebarCollapsed ? "w-16" : "w-64"
      )}
    >
      <div className="flex h-full flex-col">
        {/* Logo/Brand */}
        <div className="flex h-16 items-center justify-between border-b px-4">
          {!sidebarCollapsed && (
            <span className="text-lg font-semibold">E-Commerce</span>
          )}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
            className="h-8 w-8"
          >
            {sidebarCollapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </Button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 space-y-1 p-4">
          {navigation.map((item) => {
            const isActive = pathname === item.href;
            return (
              <Link
                key={item.name}
                href={item.href}
                onClick={() => trackNavClick(item.name, item.href)}
              >
                <div
                  className={cn(
                    "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors",
                    isActive
                      ? "bg-primary text-primary-foreground"
                      : "text-muted-foreground hover:bg-muted hover:text-foreground",
                    sidebarCollapsed && "justify-center px-2"
                  )}
                >
                  <item.icon className="h-5 w-5 flex-shrink-0" />
                  {!sidebarCollapsed && <span>{item.name}</span>}
                </div>
              </Link>
            );
          })}

          {/* Commerce Dropdown */}
          <div className="space-y-1">
            <button
              onClick={() => setCommerceDropdownOpen(!commerceDropdownOpen)}
              className={cn(
                "flex w-full items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors",
                "text-muted-foreground hover:bg-muted hover:text-foreground",
                sidebarCollapsed && "justify-center px-2"
              )}
            >
              <Store className="h-5 w-5 flex-shrink-0" />
              {!sidebarCollapsed && (
                <>
                  <span>Commerce</span>
                  <ChevronDown
                    className={cn(
                      "ml-auto h-4 w-4 transition-transform",
                      commerceDropdownOpen && "rotate-180"
                    )}
                  />
                </>
              )}
            </button>

            {commerceDropdownOpen && !sidebarCollapsed && (
              <div className="ml-6 space-y-1">
                {commerceNavigation.map((item) => {
                  const isActive = pathname === item.href;
                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      onClick={() => trackNavClick(item.name, item.href)}
                    >
                      <div
                        className={cn(
                          "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors",
                          isActive
                            ? "bg-primary text-primary-foreground"
                            : "text-muted-foreground hover:bg-muted hover:text-foreground"
                        )}
                      >
                        <item.icon className="h-4 w-4 flex-shrink-0" />
                        <span>{item.name}</span>
                      </div>
                    </Link>
                  );
                })}
              </div>
            )}
          </div>
        </nav>

        {/* Help Section */}
        <div className="border-t p-4">
          {!sidebarCollapsed && (
            <h3 className="mb-2 px-3 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
              Help & Support
            </h3>
          )}
          <div className="space-y-1">
            {helpNavigation.map((item) => {
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  onClick={() => trackNavClick(item.name, item.href)}
                >
                  <div
                    className={cn(
                      "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors",
                      isActive
                        ? "bg-primary text-primary-foreground"
                        : "text-muted-foreground hover:bg-muted hover:text-foreground",
                      sidebarCollapsed && "justify-center px-2"
                    )}
                  >
                    <item.icon className="h-5 w-5 flex-shrink-0" />
                    {!sidebarCollapsed && <span>{item.name}</span>}
                  </div>
                </Link>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};
