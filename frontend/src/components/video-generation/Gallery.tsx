"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { 
  Search, 
  Filter, 
  Download, 
  Trash2, 
  Upload, 
  MoreHorizontal, 
  Play, 
  Heart, 
  Calendar,
  Tag,
  Grid3X3,
  List,
  ChevronLeft,
  ChevronRight
} from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { VideoService as videoService } from "@/services/videoService";
import { toast } from "react-hot-toast";

interface GalleryItem {
  id: string;
  variantName: string;
  productId: string;
  productTitle: string;
  videoUrl: string;
  thumbnailUrl?: string;
  duration: number;
  createdAt: string;
  status: 'ready' | 'generating' | 'failed';
  isFavorite: boolean;
  tags: string[];
  metrics: {
    views: number;
    plays: number;
    completionRate: number;
  };
}

export function Gallery() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('created_desc');
  const [filterStatus, setFilterStatus] = useState<string>('');
  const [filterTag, setFilterTag] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(1);
  
  const pageSize = 24;

  // Fetch gallery items
  const { data: galleryData, isLoading, refetch } = useQuery({
    queryKey: ['gallery', searchQuery, sortBy, filterStatus, filterTag, currentPage],
    queryFn: () => videoService.getGallery({
      search: searchQuery,
      sortBy,
      status: filterStatus,
      tag: filterTag,
      page: currentPage,
      limit: pageSize
    }),
  });

  // Fetch available tags for filtering
  const { data: tagsData } = useQuery({
    queryKey: ['gallery-tags'],
    queryFn: () => videoService.getGalleryTags(),
  });

  const items = galleryData?.items || [];
  const totalPages = Math.ceil((galleryData?.total || 0) / pageSize);
  const tags = tagsData?.tags || [];

  const handleItemSelect = (itemId: string, checked: boolean) => {
    if (checked) {
      setSelectedItems([...selectedItems, itemId]);
    } else {
      setSelectedItems(selectedItems.filter(id => id !== itemId));
    }
  };

  const handleSelectAll = () => {
    const allItemIds = items.map(item => item.id);
    setSelectedItems(allItemIds);
  };

  const handleDeselectAll = () => {
    setSelectedItems([]);
  };

  const handleBulkDownload = async () => {
    if (selectedItems.length === 0) return;

    try {
      const downloadUrl = await videoService.createBulkDownload(selectedItems);
      window.open(downloadUrl, '_blank');
      toast.success('Bulk download started');
    } catch (error) {
      toast.error('Failed to create bulk download');
    }
  };

  const handleBulkDelete = async () => {
    if (selectedItems.length === 0) return;

    if (!confirm(`Are you sure you want to delete ${selectedItems.length} video${selectedItems.length !== 1 ? 's' : ''}?`)) {
      return;
    }

    try {
      await videoService.bulkDelete(selectedItems);
      setSelectedItems([]);
      refetch();
      toast.success('Videos deleted successfully');
    } catch (error) {
      toast.error('Failed to delete videos');
    }
  };

  const handleBulkPush = () => {
    if (selectedItems.length === 0) return;
    // This would open the push modal with selected items
    toast.info('Bulk push feature coming soon');
  };

  const isAllSelected = items.length > 0 && items.every(item => selectedItems.includes(item.id));
  const isSomeSelected = items.some(item => selectedItems.includes(item.id));

  const sortOptions = [
    { value: 'created_desc', label: 'Newest First' },
    { value: 'created_asc', label: 'Oldest First' },
    { value: 'name_asc', label: 'Name A-Z' },
    { value: 'name_desc', label: 'Name Z-A' },
    { value: 'views_desc', label: 'Most Viewed' },
    { value: 'plays_desc', label: 'Most Played' },
  ];

  const statusOptions = [
    { value: '', label: 'All Status' },
    { value: 'ready', label: 'Ready' },
    { value: 'generating', label: 'Generating' },
    { value: 'failed', label: 'Failed' },
  ];

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="flex flex-col lg:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search videos..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <div className="flex gap-2">
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {sortOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              {statusOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={filterTag} onValueChange={setFilterTag}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Tags" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Tags</SelectItem>
              {tags.map((tag) => (
                <SelectItem key={tag} value={tag}>
                  <div className="flex items-center">
                    <Tag className="mr-2 h-3 w-3" />
                    {tag}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <div className="flex border rounded-md">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
              className="rounded-r-none"
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="rounded-l-none"
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Selection Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Checkbox
            checked={isAllSelected}
            onCheckedChange={(checked) => {
              if (checked) {
                handleSelectAll();
              } else {
                handleDeselectAll();
              }
            }}
            ref={(el) => {
              if (el) {
                el.indeterminate = isSomeSelected && !isAllSelected;
              }
            }}
          />
          <span className="text-sm text-muted-foreground">
            {selectedItems.length > 0 
              ? `${selectedItems.length} video${selectedItems.length !== 1 ? 's' : ''} selected`
              : "Select videos"
            }
          </span>
        </div>

        {selectedItems.length > 0 && (
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={handleDeselectAll}>
              Clear Selection
            </Button>
            <Button variant="outline" size="sm" onClick={handleBulkDownload}>
              <Download className="mr-2 h-4 w-4" />
              Download ({selectedItems.length})
            </Button>
            <Button variant="outline" size="sm" onClick={handleBulkPush}>
              <Upload className="mr-2 h-4 w-4" />
              Push to Shopify
            </Button>
            <Button variant="destructive" size="sm" onClick={handleBulkDelete}>
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>
          </div>
        )}
      </div>

      {/* Gallery Grid/List */}
      {viewMode === 'grid' ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {items.map((item) => (
            <Card 
              key={item.id} 
              className={`cursor-pointer transition-all hover:shadow-md ${
                selectedItems.includes(item.id) ? 'ring-2 ring-primary' : ''
              }`}
            >
              <CardContent className="p-4">
                <div className="relative">
                  <div className="aspect-video bg-gray-100 rounded-lg overflow-hidden mb-3">
                    {item.thumbnailUrl ? (
                      <img
                        src={item.thumbnailUrl}
                        alt={item.variantName}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <Play className="h-8 w-8 text-gray-400" />
                      </div>
                    )}
                    
                    {/* Duration */}
                    <div className="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                      {Math.floor(item.duration / 60)}:{(item.duration % 60).toString().padStart(2, '0')}
                    </div>

                    {/* Favorite */}
                    {item.isFavorite && (
                      <div className="absolute top-2 left-2">
                        <Heart className="h-4 w-4 text-red-500 fill-current" />
                      </div>
                    )}
                  </div>

                  <Checkbox
                    checked={selectedItems.includes(item.id)}
                    onCheckedChange={(checked) => handleItemSelect(item.id, checked as boolean)}
                    className="absolute top-2 right-2 bg-white shadow-sm"
                  />
                </div>

                <div className="space-y-2">
                  <h3 className="font-medium text-sm line-clamp-1">{item.variantName}</h3>
                  <p className="text-xs text-muted-foreground line-clamp-1">{item.productTitle}</p>
                  
                  <div className="flex items-center justify-between">
                    <Badge variant={
                      item.status === 'ready' ? 'default' : 
                      item.status === 'generating' ? 'secondary' : 'destructive'
                    } className="text-xs">
                      {item.status}
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      {item.metrics.views} views
                    </span>
                  </div>

                  {item.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {item.tags.slice(0, 2).map((tag) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                      {item.tags.length > 2 && (
                        <Badge variant="outline" className="text-xs">
                          +{item.tags.length - 2}
                        </Badge>
                      )}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="space-y-2">
          {items.map((item) => (
            <Card 
              key={item.id}
              className={`${selectedItems.includes(item.id) ? 'ring-2 ring-primary' : ''}`}
            >
              <CardContent className="p-4">
                <div className="flex items-center space-x-4">
                  <Checkbox
                    checked={selectedItems.includes(item.id)}
                    onCheckedChange={(checked) => handleItemSelect(item.id, checked as boolean)}
                  />
                  
                  <div className="w-16 h-10 bg-gray-100 rounded overflow-hidden flex-shrink-0">
                    {item.thumbnailUrl ? (
                      <img
                        src={item.thumbnailUrl}
                        alt={item.variantName}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <Play className="h-4 w-4 text-gray-400" />
                      </div>
                    )}
                  </div>

                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-sm truncate">{item.variantName}</h3>
                    <p className="text-xs text-muted-foreground truncate">{item.productTitle}</p>
                  </div>

                  <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                    <span>{item.metrics.views} views</span>
                    <span>{item.metrics.plays} plays</span>
                    <span>{item.metrics.completionRate}% completion</span>
                  </div>

                  <Badge variant={
                    item.status === 'ready' ? 'default' : 
                    item.status === 'generating' ? 'secondary' : 'destructive'
                  }>
                    {item.status}
                  </Badge>

                  <span className="text-xs text-muted-foreground">
                    {new Date(item.createdAt).toLocaleDateString()}
                  </span>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <Download className="mr-2 h-4 w-4" />
                        Download
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Upload className="mr-2 h-4 w-4" />
                        Push to Shopify
                      </DropdownMenuItem>
                      <DropdownMenuItem className="text-red-600">
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            Page {currentPage} of {totalPages} • {galleryData?.total || 0} total videos
          </p>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {items.length === 0 && !isLoading && (
        <Card className="p-12">
          <div className="text-center text-muted-foreground">
            <Play className="mx-auto h-12 w-12 mb-4 opacity-50" />
            <p>No videos found.</p>
            <p className="text-sm mt-2">Try adjusting your search or filters.</p>
          </div>
        </Card>
      )}
    </div>
  );
}
