"use client";

import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, <PERSON><PERSON><PERSON>ooter, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { Upload, CheckCircle, AlertCircle, Loader2, Play, Image as ImageIcon } from "lucide-react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { VideoService as videoService } from "@/services/videoService";
import { ShopifyService as shopifyService } from "@/services/shopifyService";
import { toast } from "react-hot-toast";

interface PushModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedVariants: string[];
}

interface PushJob {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress?: number;
  results?: Array<{
    variantId: string;
    productId: string;
    success: boolean;
    mediaId?: string;
    error?: string;
  }>;
}

export function PushModal({ open, onOpenChange, selectedVariants }: PushModalProps) {
  const [step, setStep] = useState<'configure' | 'pushing' | 'completed'>('configure');
  const [altTexts, setAltTexts] = useState<Record<string, string>>({});
  const [mediaPositions, setMediaPositions] = useState<Record<string, number>>({});
  const [replaceExisting, setReplaceExisting] = useState(false);
  const [currentJob, setCurrentJob] = useState<PushJob | null>(null);

  // Fetch variant details
  const { data: variantsData } = useQuery({
    queryKey: ['video-variants', selectedVariants],
    queryFn: () => videoService.getVariantsByIds(selectedVariants),
    enabled: open && selectedVariants.length > 0,
  });

  // Fetch connected Shopify stores
  const { data: storesData } = useQuery({
    queryKey: ['shopify-stores'],
    queryFn: () => shopifyService.getConnectedStores(),
    enabled: open,
  });

  const variants = variantsData?.variants || [];
  const stores = storesData?.stores || [];

  // Push to Shopify mutation
  const pushMutation = useMutation({
    mutationFn: (params: {
      variantIds: string[];
      altTexts: Record<string, string>;
      mediaPositions: Record<string, number>;
      replaceExisting: boolean;
    }) => videoService.pushToShopify(params),
    onSuccess: (data) => {
      setCurrentJob(data.job);
      setStep('pushing');
      // Start polling for job status
      pollPushStatus(data.job.id);
    },
    onError: (error) => {
      toast.error('Failed to start push to Shopify');
      console.error('Push error:', error);
    },
  });

  // Poll push status
  const pollPushStatus = async (jobId: string) => {
    const pollInterval = setInterval(async () => {
      try {
        const status = await videoService.getPushJobStatus(jobId);
        setCurrentJob(status);
        
        if (status.status === 'completed') {
          clearInterval(pollInterval);
          setStep('completed');
          const successCount = status.results?.filter(r => r.success).length || 0;
          const failCount = status.results?.filter(r => !r.success).length || 0;
          
          if (failCount === 0) {
            toast.success(`Successfully pushed ${successCount} videos to Shopify!`);
          } else {
            toast.error(`${successCount} videos pushed successfully, ${failCount} failed`);
          }
        } else if (status.status === 'failed') {
          clearInterval(pollInterval);
          toast.error('Push to Shopify failed');
        }
      } catch (error) {
        console.error('Failed to poll push status:', error);
        clearInterval(pollInterval);
      }
    }, 2000);

    // Clean up after 10 minutes
    setTimeout(() => clearInterval(pollInterval), 10 * 60 * 1000);
  };

  const handlePush = () => {
    if (selectedVariants.length === 0) {
      toast.error('No variants selected');
      return;
    }

    pushMutation.mutate({
      variantIds: selectedVariants,
      altTexts,
      mediaPositions,
      replaceExisting,
    });
  };

  const handleClose = () => {
    if (step === 'pushing') {
      // Warn user about closing during push
      if (confirm('Push to Shopify is in progress. Are you sure you want to close?')) {
        onOpenChange(false);
        resetModal();
      }
    } else {
      onOpenChange(false);
      resetModal();
    }
  };

  const resetModal = () => {
    setStep('configure');
    setCurrentJob(null);
    setAltTexts({});
    setMediaPositions({});
    setReplaceExisting(false);
  };

  const handleAltTextChange = (variantId: string, altText: string) => {
    setAltTexts(prev => ({ ...prev, [variantId]: altText }));
  };

  const handlePositionChange = (variantId: string, position: number) => {
    setMediaPositions(prev => ({ ...prev, [variantId]: position }));
  };

  const getProgressValue = () => {
    if (!currentJob) return 0;
    if (currentJob.progress) return currentJob.progress;
    if (currentJob.status === 'completed') return 100;
    return 0;
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Upload className="mr-2 h-5 w-5 text-blue-600" />
            Push Videos to Shopify
          </DialogTitle>
          <DialogDescription>
            {step === 'configure' && `Configure and push ${selectedVariants.length} video${selectedVariants.length !== 1 ? 's' : ''} to your Shopify store`}
            {step === 'pushing' && 'Pushing videos to Shopify...'}
            {step === 'completed' && 'Push to Shopify completed!'}
          </DialogDescription>
        </DialogHeader>

        {step === 'configure' && (
          <div className="space-y-6">
            {/* Store Selection */}
            {stores.length > 1 && (
              <div className="space-y-3">
                <Label>Shopify Store</Label>
                <RadioGroup defaultValue={stores[0]?.id}>
                  {stores.map((store) => (
                    <div key={store.id} className="flex items-center space-x-2">
                      <RadioGroupItem value={store.id} id={store.id} />
                      <Label htmlFor={store.id} className="flex items-center space-x-2">
                        <span>{store.name}</span>
                        <Badge variant="outline">{store.domain}</Badge>
                      </Label>
                    </div>
                  ))}
                </RadioGroup>
              </div>
            )}

            {/* Push Options */}
            <div className="space-y-3">
              <Label>Push Options</Label>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="replace-existing"
                  checked={replaceExisting}
                  onCheckedChange={setReplaceExisting}
                />
                <Label htmlFor="replace-existing" className="text-sm">
                  Replace existing product videos
                </Label>
              </div>
              <p className="text-xs text-muted-foreground">
                If enabled, existing videos will be replaced. Otherwise, videos will be added to the product media.
              </p>
            </div>

            <Separator />

            {/* Video Configuration */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Configure Videos</h3>
              
              <div className="grid gap-4">
                {variants.map((variant, index) => (
                  <Card key={variant.id}>
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-sm">{variant.variantName}</CardTitle>
                        <Badge variant="outline">Product {variant.productId}</Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex space-x-4">
                        {/* Video Preview */}
                        <div className="w-32 h-18 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                          {variant.thumbnailUrl ? (
                            <img 
                              src={variant.thumbnailUrl} 
                              alt={variant.variantName}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="flex items-center justify-center h-full">
                              <Play className="h-6 w-6 text-gray-400" />
                            </div>
                          )}
                        </div>

                        {/* Configuration */}
                        <div className="flex-1 space-y-3">
                          <div>
                            <Label htmlFor={`alt-text-${variant.id}`} className="text-xs">
                              Alt Text
                            </Label>
                            <Input
                              id={`alt-text-${variant.id}`}
                              placeholder={`${variant.variantName} product video`}
                              value={altTexts[variant.id] || ''}
                              onChange={(e) => handleAltTextChange(variant.id, e.target.value)}
                              className="text-sm"
                            />
                          </div>

                          <div>
                            <Label htmlFor={`position-${variant.id}`} className="text-xs">
                              Media Position
                            </Label>
                            <Input
                              id={`position-${variant.id}`}
                              type="number"
                              min="1"
                              placeholder={`${index + 1}`}
                              value={mediaPositions[variant.id] || ''}
                              onChange={(e) => handlePositionChange(variant.id, parseInt(e.target.value) || 1)}
                              className="text-sm w-20"
                            />
                            <p className="text-xs text-muted-foreground mt-1">
                              Position in product media gallery (1 = first)
                            </p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            <Separator />

            {/* Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Push Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Videos to push:</span>
                  <span>{selectedVariants.length}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Target store:</span>
                  <span>{stores[0]?.name || 'No store connected'}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Replace existing:</span>
                  <span>{replaceExisting ? 'Yes' : 'No'}</span>
                </div>
                <Separator />
                <p className="text-xs text-muted-foreground">
                  Videos will be uploaded to Shopify and added to the respective product pages.
                  This process may take a few minutes depending on video sizes.
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {step === 'pushing' && currentJob && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Pushing to Shopify
                </CardTitle>
                <CardDescription>
                  Job ID: {currentJob.id}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress</span>
                    <span>{getProgressValue()}%</span>
                  </div>
                  <Progress value={getProgressValue()} />
                </div>

                <div className="space-y-2">
                  <p className="text-sm font-medium">Status: {currentJob.status}</p>
                  {currentJob.results && (
                    <div className="space-y-2">
                      <p className="text-sm text-muted-foreground">Results:</p>
                      {currentJob.results.map((result) => (
                        <div key={result.variantId} className="flex items-center justify-between text-sm">
                          <span>Variant {result.variantId} → Product {result.productId}</span>
                          <div className="flex items-center space-x-2">
                            {result.success ? (
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            ) : (
                              <AlertCircle className="h-4 w-4 text-red-500" />
                            )}
                            <Badge variant={result.success ? 'default' : 'destructive'}>
                              {result.success ? 'Success' : 'Failed'}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {step === 'completed' && currentJob && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center text-green-600">
                  <CheckCircle className="mr-2 h-5 w-5" />
                  Push Complete!
                </CardTitle>
                <CardDescription>
                  Videos have been pushed to Shopify
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {currentJob.results?.map((result) => (
                    <div key={result.variantId} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-8 bg-gray-100 rounded overflow-hidden">
                          <ImageIcon className="w-full h-full p-2 text-gray-400" />
                        </div>
                        <div>
                          <p className="text-sm font-medium">Variant {result.variantId}</p>
                          <p className="text-xs text-muted-foreground">Product {result.productId}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {result.success ? (
                          <>
                            <CheckCircle className="h-4 w-4 text-green-500" />
                            <Badge variant="default">Pushed</Badge>
                            {result.mediaId && (
                              <Badge variant="outline" className="text-xs">
                                Media ID: {result.mediaId}
                              </Badge>
                            )}
                          </>
                        ) : (
                          <>
                            <AlertCircle className="h-4 w-4 text-red-500" />
                            <Badge variant="destructive">Failed</Badge>
                            {result.error && (
                              <p className="text-xs text-red-600">{result.error}</p>
                            )}
                          </>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        <DialogFooter>
          {step === 'configure' && (
            <>
              <Button variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button 
                onClick={handlePush}
                disabled={selectedVariants.length === 0 || pushMutation.isPending || stores.length === 0}
              >
                {pushMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Starting...
                  </>
                ) : (
                  <>
                    <Upload className="mr-2 h-4 w-4" />
                    Push to Shopify
                  </>
                )}
              </Button>
            </>
          )}
          
          {step === 'pushing' && (
            <Button variant="outline" onClick={handleClose}>
              Close
            </Button>
          )}
          
          {step === 'completed' && (
            <Button onClick={handleClose}>
              Done
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
