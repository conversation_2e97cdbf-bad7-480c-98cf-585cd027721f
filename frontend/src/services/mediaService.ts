import { api } from "./api";

export interface MediaJob {
  id: string;
  status: string;
  productCount: number;
  variantCount: number;
  createdAt: string;
  completedAt?: string;
  progress?: number;
}

export interface UsageStats {
  videosGenerated: number;
  monthlyLimit: number;
  storageUsed: number;
  storageLimit: number;
}

export const mediaService = {
  async getRecentJobs(): Promise<MediaJob[]> {
    try {
      const response = await api.get("/api/media/jobs?per_page=5");
      return response.data.jobs || [];
    } catch (err) {
      console.error('Failed to fetch recent jobs:', err);
      return [];
    }
  },

  async getUsageStats(): Promise<UsageStats> {
    // TODO: Replace with actual backend endpoint when available
    // For now, return default stats
    return {
      videosGenerated: 0,
      monthlyLimit: 500,
      storageUsed: 0,
      storageLimit: 10
    };
  },
};