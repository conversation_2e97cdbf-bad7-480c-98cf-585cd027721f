import { api } from "./api";

export interface Product {
  id: string;
  external_id: string;
  title: string;
  description?: string;
  price: number;
  compare_at_price?: number;
  cost?: number;
  sku?: string;
  barcode?: string;
  quantity: number;
  weight?: number;
  weight_unit: string;
  vendor?: string;
  product_type?: string;
  tags?: string;
  status: string;
  published: boolean;
  handle?: string;
  images?: string;
  featured_image?: string;
  full_json?: string;
  store_id: number;
  created_at: string;
  updated_at: string;

  // Additional Shopify fields
  published_at?: string;
  online_store_url?: string;
  online_store_preview_url?: string;
  has_only_default_variant: boolean;
  has_out_of_stock_variants: boolean;
  is_gift_card: boolean;
  requires_selling_plan: boolean;
  total_inventory: number;
  total_variants: number;
  tracks_inventory: boolean;
  template_suffix?: string;
  gift_card_template_suffix?: string;
  legacy_resource_id?: string;
  options?: string;
  product_category?: string;
  seo?: string;
  metafields?: string;
  collections?: string;
  compare_at_price_range?: string;
  price_range?: string;
}

export const productService = {
  async getProducts(page: number = 1, limit: number = 50): Promise<Product[]> {
    const response = await api.get(`/api/products/?page=${page}&limit=${limit}`);
    return response.data;
  },

  async getProduct(id: string): Promise<Product> {
    const response = await api.get(`/api/products/${id}`);
    return response.data;
  },
};