/**
 * Shopify Integration Service - Frontend API client
 */

import { api } from "./api";

export interface ShopifyProduct {
  id: string;
  title: string;
  description?: string;
  handle?: string;
  product_type?: string;
  vendor?: string;
  status?: string;
  tags?: string;
  featured_image_url?: string;
  images?: string[];
  created_at?: string;
  updated_at?: string;
}

export interface ShopifyStore {
  id: number;
  name: string;
  shop_domain: string;
  shop_name?: string;
  is_active: boolean;
  last_sync?: string;
}

export interface ShopifyOAuthResponse {
  install_url: string;
  message: string;
}

export interface ShopifyCallbackResponse {
  message: string;
  store: {
    id: number;
    name: string;
    shop_domain: string;
    shop_name: string;
  };
}

/**
 * Shopify Integration API Service
 */
export class ShopifyService {
  /**
   * Get Shopify app installation URL
   */
  static async getInstallUrl(
    shopDomain: string
  ): Promise<ShopifyOAuthResponse> {
    const response = await api.get(
      `/api/shopify/install?shop=${encodeURIComponent(shopDomain)}`
    );
    return response.data;
  }

  /**
   * Handle OAuth callback (called by backend redirect)
   */
  static async handleOAuthCallback(
    code: string,
    shop: string,
    state?: string
  ): Promise<ShopifyCallbackResponse> {
    const params = new URLSearchParams();
    params.append("code", code);
    params.append("shop", shop);
    if (state) params.append("state", state);

    const response = await api.get(`/api/shopify/oauth/callback?${params}`);
    return response.data;
  }

  /**
   * Get required Shopify scopes
   */
  static async getRequiredScopes(): Promise<{
    scopes: string[];
    description: string;
  }> {
    const response = await api.get("/api/shopify/scopes");
    return response.data;
  }

  /**
   * Verify webhook signature (for testing)
   */
  static async verifyWebhook(payload: any, signature: string) {
    const response = await api.post("/api/shopify/webhook/verify", payload, {
      headers: {
        "X-Shopify-Hmac-Sha256": signature,
      },
    });
    return response.data;
  }

  /**
   * Get products from connected Shopify store
   */
  static async getProducts(
    storeId: number,
    limit: number = 50,
    offset: number = 0
  ): Promise<{ products: ShopifyProduct[]; total: number }> {
    const response = await api.get(
      `/api/stores/${storeId}/products?limit=${limit}&offset=${offset}`
    );
    return response.data;
  }

  /**
   * Get single product details
   */
  static async getProduct(
    storeId: number,
    productId: string
  ): Promise<ShopifyProduct> {
    const response = await api.get(
      `/api/stores/${storeId}/products/${productId}`
    );
    return response.data;
  }

  /**
   * Sync products from Shopify
   */
  static async syncProducts(storeId: number) {
    const response = await api.post(`/api/stores/${storeId}/sync/products`);
    return response.data;
  }

  /**
   * Get sync status
   */
  static async getSyncStatus(storeId: number) {
    const response = await api.get(`/api/stores/${storeId}/sync/status`);
    return response.data;
  }

  /**
   * Test store connection
   */
  static async testConnection(storeId: number) {
    const response = await api.post(`/api/stores/${storeId}/test-connection`);
    return response.data;
  }

  /**
   * Get webhook statistics
   */
  static async getWebhookStats() {
    const response = await api.get("/api/webhooks/stats");
    return response.data;
  }

  /**
   * Subscribe to webhooks for a store
   */
  static async subscribeWebhooks(storeId: number) {
    const response = await api.post(
      `/api/stores/${storeId}/webhooks/subscribe`
    );
    return response.data;
  }

  /**
   * Unsubscribe from webhooks
   */
  static async unsubscribeWebhooks(storeId: number) {
    const response = await api.post(
      `/api/stores/${storeId}/webhooks/unsubscribe`
    );
    return response.data;
  }

  /**
   * Get store analytics
   */
  static async getStoreAnalytics(
    storeId: number,
    fromDate?: string,
    toDate?: string
  ) {
    const params = new URLSearchParams();
    if (fromDate) params.append("from_date", fromDate);
    if (toDate) params.append("to_date", toDate);

    const response = await api.get(
      `/api/stores/${storeId}/analytics?${params}`
    );
    return response.data;
  }

  /**
   * Search products
   */
  static async searchProducts(
    storeId: number,
    query: string,
    limit: number = 20
  ): Promise<{ products: ShopifyProduct[] }> {
    const response = await api.get(
      `/api/stores/${storeId}/products/search?q=${encodeURIComponent(query)}&limit=${limit}`
    );
    return response.data;
  }

  /**
   * Get product variants
   */
  static async getProductVariants(storeId: number, productId: string) {
    const response = await api.get(
      `/api/stores/${storeId}/products/${productId}/variants`
    );
    return response.data;
  }

  /**
   * Update product media (after video generation)
   */
  static async updateProductMedia(
    storeId: number,
    productId: string,
    mediaData: {
      video_url: string;
      alt_text?: string;
      position?: number;
    }
  ) {
    const response = await api.post(
      `/api/stores/${storeId}/products/${productId}/media`,
      mediaData
    );
    return response.data;
  }

  /**
   * Get products with enhanced interface for video generation
   */
  static async getProducts(params: {
    search?: string;
    tag?: string;
    collection?: string;
    page?: number;
    limit?: number;
  }) {
    const response = await api.get("/api/shopify/products", { params });
    return response.data;
  }

  /**
   * Get collections
   */
  static async getCollections() {
    const response = await api.get("/api/shopify/collections");
    return response.data;
  }

  /**
   * Get product tags
   */
  static async getProductTags() {
    const response = await api.get("/api/shopify/product-tags");
    return response.data;
  }

  /**
   * Get connected stores
   */
  static async getConnectedStores() {
    const response = await api.get("/api/shopify/stores");
    return response.data;
  }
}

export default ShopifyService;
