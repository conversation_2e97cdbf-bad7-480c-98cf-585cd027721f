import { <PERSON> } from "react-router-dom";
import { Button } from "@/components/ui/button";
import FiesonLogo from "@/components/FiesonLogo";
import useAnalytics from "@/hooks/use-analytics";
import useBookDemo from "@/hooks/use-book-demo";

const Footer = () => {
  const { trackEvent } = useAnalytics();
  const { handleBookDemoClick } = useBookDemo({
    label: "Footer - Book a Demo Button",
  });

  const industries = [
    { name: "Commercial Cleaning", href: "/industries/commercial-cleaning" },
    { name: "Window Cleaning", href: "/industries/window-cleaning" },
    { name: "HVAC", href: "/industries/hvac" },
    { name: "Roofing", href: "/industries/roofing" },
    { name: "Solar", href: "/industries/solar" },
    { name: "Pest Control", href: "/industries/pest-control" },
    { name: "Plumbing Industry", href: "/industries/plumbing" },
  ];

  const company = [
    { name: "Home", href: "/" },
    { name: "About", href: "/about" },
    { name: "Pricing", href: "/pricing" },
    { name: "Integrations", href: "/integrations" },
    { name: "Contact Us", href: "/book-a-demo" },
  ];

  const handleNavLinkClick = (name: string, href: string) => {
    trackEvent({
      action: "navigation_click",
      category: "Footer Navigation",
      label: `Nav Link: ${name} (${href})`,
    });
  };

  const handleTalkToFiesonAIClick = () => {
    trackEvent({
      action: "button_click",
      category: "Footer Call to Action",
      label: "Talk to Fieson AI Button",
    });
    window.dispatchEvent(new Event("focusPhoneInput"));
  };

  return (
    <footer className="bg-primary text-primary-foreground">
      <div className="container mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 xl:px-12 2xl:px-24 py-16">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {/* Industries */}
          <div>
            <h3 className="font-semibold text-lg mb-4 uppercase tracking-wide">
              INDUSTRIES
            </h3>
            <ul className="space-y-3">
              {industries.map((industry) => (
                <li key={industry.name}>
                  <Link
                    to={industry.href}
                    className="text-primary-foreground/80 hover:text-primary-foreground smooth-transition"
                    onClick={() =>
                      handleNavLinkClick(industry.name, industry.href)
                    }
                  >
                    {industry.name}
                  </Link>
                </li>
              ))}
              <li>
                <Link
                  to="/industries"
                  className="text-primary-foreground/80 hover:text-primary-foreground smooth-transition font-bold"
                  onClick={() =>
                    handleNavLinkClick("View All Industries", "/industries")
                  }
                >
                  View All Industries
                </Link>
              </li>
            </ul>
          </div>

          {/* Resources */}
          <div>
            <h3 className="font-semibold text-lg mb-4 uppercase tracking-wide">
              RESOURCES
            </h3>
            <ul className="space-y-3">
              <li>
                <Link
                  to="/resources"
                  className="text-primary-foreground/80 hover:text-primary-foreground smooth-transition"
                  onClick={() => handleNavLinkClick("Articles", "/resources")}
                >
                  Articles
                </Link>
              </li>
            </ul>
          </div>

          {/* Company */}
          <div>
            <h3 className="font-semibold text-lg mb-4 uppercase tracking-wide">
              COMPANY
            </h3>
            <ul className="space-y-3">
              {company.map((item) => (
                <li key={item.name}>
                  <Link
                    to={item.href}
                    className="text-primary-foreground/80 hover:text-primary-foreground smooth-transition"
                    onClick={() => handleNavLinkClick(item.name, item.href)}
                  >
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* CTA */}
          <div>
            <h3 className="font-semibold text-lg mb-4 uppercase tracking-wide">
              GET STARTED
            </h3>
            <div className="space-y-4">
              <Button
                variant="demo"
                size="lg"
                className="w-full"
                onClick={handleBookDemoClick}
              >
                Book a demo
              </Button>
              <Button
                variant="hero"
                size="xl"
                className="w-full h-14 text-lg font-semibold"
                onClick={handleTalkToFiesonAIClick}
              >
                Talk to Fieson AI
              </Button>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-primary-foreground/20 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            {/* Logo */}
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 rounded-lg flex items-center justify-center">
                <FiesonLogo white className="w-full h-full" />
              </div>
              <span className="text-xl font-bold">Fieson AI</span>
            </div>

            {/* Legal */}
            <div className="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6 text-sm">
              <Link
                to="/privacy/terms-europe"
                className="text-primary-foreground/80 hover:text-primary-foreground smooth-transition uppercase"
                onClick={() =>
                  handleNavLinkClick("Terms of Use", "/privacy/terms-europe")
                }
              >
                TERMS OF USE
              </Link>
              <Link
                to="/privacy/terms-europe"
                className="text-primary-foreground/80 hover:text-primary-foreground smooth-transition uppercase"
                onClick={() =>
                  handleNavLinkClick("Privacy Policy", "/privacy/terms-europe")
                }
              >
                privacy policy
              </Link>
            </div>

            {/* Copyright */}
            <div className="text-sm text-primary-foreground/80">
              © Fieson AI Inc. 2025
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
